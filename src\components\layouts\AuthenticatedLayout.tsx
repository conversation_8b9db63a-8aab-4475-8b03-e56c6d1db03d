import React from 'react';
import { Outlet } from 'react-router-dom';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/navigation/AppSidebar';
import { Separator } from '@/components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

// Helper function to generate breadcrumbs from pathname
const generateBreadcrumbs = (pathname: string) => {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs = [];

  // Always start with Dashboard
  breadcrumbs.push({
    label: 'Dashboard',
    href: '/dashboard',
    isActive: pathname === '/dashboard',
  });

  // Generate breadcrumbs for nested paths
  if (segments.length > 1) {
    let currentPath = '';
    
    for (let i = 1; i < segments.length; i++) {
      currentPath += `/${segments[i]}`;
      const fullPath = `/${segments[0]}${currentPath}`;
      const isLast = i === segments.length - 1;
      
      // Convert segment to readable label
      const label = segments[i]
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      breadcrumbs.push({
        label,
        href: fullPath,
        isActive: isLast,
      });
    }
  }

  return breadcrumbs;
};

// Header component for authenticated pages
const AuthenticatedHeader: React.FC = () => {
  const location = useLocation();
  const { user } = useAuth();
  const breadcrumbs = generateBreadcrumbs(location.pathname);

  const getPageTitle = () => {
    const currentBreadcrumb = breadcrumbs[breadcrumbs.length - 1];
    return currentBreadcrumb?.label || 'Dashboard';
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.map((breadcrumb, index) => (
              <React.Fragment key={breadcrumb.href}>
                <BreadcrumbItem className="hidden md:block">
                  {breadcrumb.isActive ? (
                    <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={breadcrumb.href}>
                      {breadcrumb.label}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
                {index < breadcrumbs.length - 1 && (
                  <BreadcrumbSeparator className="hidden md:block" />
                )}
              </React.Fragment>
            ))}
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      
      {/* Page title and greeting for mobile */}
      <div className="ml-auto px-4 md:hidden">
        <div className="text-right">
          <h1 className="text-lg font-semibold">{getPageTitle()}</h1>
          <p className="text-sm text-muted-foreground">
            {getGreeting()}, {user?.name.split(' ')[0]}
          </p>
        </div>
      </div>
    </header>
  );
};

// Main authenticated layout component
export const AuthenticatedLayout: React.FC = () => {
  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <AuthenticatedHeader />
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
            <main className="p-4 md:p-6">
              <Outlet />
            </main>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};
