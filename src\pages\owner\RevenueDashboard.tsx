import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DollarSign, 
  Download,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Wallet,
  PieChart,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Building2,
  Users,
  Bed,
  Target,
  Clock,
  CheckCircle
} from 'lucide-react';
import { mockPayments, mockBookings, mockHostels, mockUsers, getHostelById, getUserById } from '@/data/mockData';

export const RevenueDashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedHostel, setSelectedHostel] = useState<'all' | string>('all');

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  
  // Filter bookings and payments for owner's hostels
  const ownerBookings = mockBookings.filter(b => ownerHostels.some(h => h.id === b.hostelId));
  const ownerPayments = mockPayments.filter(p => ownerBookings.some(b => b.id === p.bookingId));

  // Calculate financial metrics
  const totalRevenue = ownerPayments.reduce((sum, p) => sum + p.amount, 0);
  const ownerEarnings = ownerPayments.reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);
  const platformCommission = ownerPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0);
  const averageBookingValue = totalRevenue / ownerBookings.length || 0;
  const totalBookings = ownerBookings.length;
  const occupancyRate = ownerHostels.reduce((sum, h) => sum + ((h.totalBeds - h.availableBeds) / h.totalBeds), 0) / ownerHostels.length * 100;

  // Revenue by hostel
  const revenueByHostel = ownerHostels.map(hostel => {
    const hostelBookings = ownerBookings.filter(b => b.hostelId === hostel.id);
    const hostelPayments = ownerPayments.filter(p => hostelBookings.some(b => b.id === p.bookingId));
    const revenue = hostelPayments.reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);
    const bookings = hostelBookings.length;
    const occupancy = ((hostel.totalBeds - hostel.availableBeds) / hostel.totalBeds) * 100;
    
    return {
      ...hostel,
      revenue,
      bookings,
      occupancy: occupancy.toFixed(1)
    };
  }).sort((a, b) => b.revenue - a.revenue);

  // Payment method breakdown
  const paymentMethods = ownerPayments.reduce((acc, payment) => {
    acc[payment.method] = (acc[payment.method] || 0) + payment.splitDetails.hostelOwner;
    return acc;
  }, {} as Record<string, number>);

  const financialStats = [
    {
      title: 'Total Earnings',
      value: `₹${(ownerEarnings / 1000).toFixed(0)}K`,
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'Your share after commission'
    },
    {
      title: 'Total Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      change: '****%',
      trend: 'up',
      icon: Wallet,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Gross revenue generated'
    },
    {
      title: 'Avg Booking Value',
      value: `₹${(averageBookingValue / 1000).toFixed(0)}K`,
      change: '****%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Average per booking'
    },
    {
      title: 'Occupancy Rate',
      value: `${occupancyRate.toFixed(1)}%`,
      change: '+2.3%',
      trend: 'up',
      icon: Building2,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Current occupancy'
    },
  ];

  const getTimeRangeLabel = (range: string) => {
    const labels = {
      '7d': 'Last 7 days',
      '30d': 'Last 30 days',
      '90d': 'Last 90 days',
      '1y': 'Last year'
    };
    return labels[range as keyof typeof labels];
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Revenue Dashboard</h1>
          <p className="text-muted-foreground">
            Track your earnings, revenue, and financial performance
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <PieChart className="mr-2 h-4 w-4" />
            Generate Invoice
          </Button>
        </div>
      </div>

      {/* Financial Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {financialStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last period</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Revenue Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Revenue Split
            </CardTitle>
            <CardDescription>
              Your earnings vs platform commission
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">Your Earnings</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">₹{(ownerEarnings / 1000).toFixed(0)}K</div>
                  <div className="text-xs text-muted-foreground">
                    {((ownerEarnings / totalRevenue) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">Platform Commission</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">₹{(platformCommission / 1000).toFixed(0)}K</div>
                  <div className="text-xs text-muted-foreground">
                    {((platformCommission / totalRevenue) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Methods
            </CardTitle>
            <CardDescription>
              Revenue breakdown by payment method
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(paymentMethods).map(([method, amount]) => {
                const percentage = ((amount / ownerEarnings) * 100).toFixed(1);
                const methodLabel = method === 'bank_transfer' ? 'Bank Transfer' : method.toUpperCase();
                const color = method === 'upi' ? 'bg-purple-500' : 
                             method === 'card' ? 'bg-blue-500' : 'bg-orange-500';
                return (
                  <div key={method} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${color}`}></div>
                      <span className="text-sm font-medium">{methodLabel}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">₹{(amount / 1000).toFixed(0)}K</div>
                      <div className="text-xs text-muted-foreground">{percentage}%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance by Hostel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Performance by Hostel
          </CardTitle>
          <CardDescription>
            Revenue and occupancy metrics for each property
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {revenueByHostel.map((hostel, index) => (
              <div key={hostel.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                    <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium">{hostel.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {hostel.city}, {hostel.state}
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-6 text-right">
                  <div>
                    <div className="font-semibold">₹{(hostel.revenue / 1000).toFixed(0)}K</div>
                    <div className="text-xs text-muted-foreground">Revenue</div>
                  </div>
                  <div>
                    <div className="font-semibold">{hostel.bookings}</div>
                    <div className="text-xs text-muted-foreground">Bookings</div>
                  </div>
                  <div>
                    <div className="font-semibold">{hostel.occupancy}%</div>
                    <div className="text-xs text-muted-foreground">Occupancy</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
