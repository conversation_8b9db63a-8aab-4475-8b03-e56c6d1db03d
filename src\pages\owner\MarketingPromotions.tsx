import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Megaphone, 
  Star,
  TrendingUp,
  TrendingDown,
  Eye,
  MessageSquare,
  Calendar,
  Target,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Edit,
  Share2,
  Camera,
  Award,
  Users,
  DollarSign,
  BarChart3,
  Zap,
  Save,
  X
} from 'lucide-react';
import { mockHostels } from '@/data/mockData';

// Mock marketing data
interface Campaign {
  id: string;
  name: string;
  type: 'discount' | 'promotion' | 'seasonal' | 'referral';
  status: 'active' | 'scheduled' | 'paused' | 'completed';
  discount: number;
  startDate: string;
  endDate: string;
  bookingsGenerated: number;
  revenue: number;
  impressions: number;
  clicks: number;
}

interface Review {
  id: string;
  guestName: string;
  rating: number;
  comment: string;
  date: string;
  hostelId: string;
  response?: string;
  isPublic: boolean;
}

const mockCampaigns: Campaign[] = [
  {
    id: '1',
    name: 'Early Bird Special',
    type: 'discount',
    status: 'active',
    discount: 20,
    startDate: '2024-01-20T00:00:00Z',
    endDate: '2024-02-29T23:59:59Z',
    bookingsGenerated: 45,
    revenue: 125000,
    impressions: 2500,
    clicks: 180
  },
  {
    id: '2',
    name: 'Weekend Getaway',
    type: 'promotion',
    status: 'scheduled',
    discount: 15,
    startDate: '2024-02-01T00:00:00Z',
    endDate: '2024-02-28T23:59:59Z',
    bookingsGenerated: 0,
    revenue: 0,
    impressions: 0,
    clicks: 0
  },
  {
    id: '3',
    name: 'Summer Special',
    type: 'seasonal',
    status: 'completed',
    discount: 25,
    startDate: '2024-01-01T00:00:00Z',
    endDate: '2024-01-15T23:59:59Z',
    bookingsGenerated: 32,
    revenue: 89000,
    impressions: 1800,
    clicks: 120
  }
];

const mockReviews: Review[] = [
  {
    id: '1',
    guestName: 'John Doe',
    rating: 5,
    comment: 'Excellent hostel with great facilities and friendly staff. The location is perfect and rooms are clean.',
    date: '2024-01-24T10:30:00Z',
    hostelId: '1',
    response: 'Thank you for your wonderful review! We\'re delighted you enjoyed your stay.',
    isPublic: true
  },
  {
    id: '2',
    guestName: 'Sarah Wilson',
    rating: 4,
    comment: 'Good value for money. The WiFi could be faster but overall a pleasant experience.',
    date: '2024-01-23T14:20:00Z',
    hostelId: '1',
    isPublic: true
  },
  {
    id: '3',
    guestName: 'Mike Johnson',
    rating: 3,
    comment: 'Average stay. The room was okay but the common areas need better maintenance.',
    date: '2024-01-22T09:15:00Z',
    hostelId: '1',
    isPublic: false
  }
];

export const MarketingPromotions: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'campaigns' | 'reviews' | 'analytics'>('campaigns');
  const [isCreateCampaignOpen, setIsCreateCampaignOpen] = useState(false);
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    type: 'discount' as Campaign['type'],
    discount: '',
    startDate: '',
    endDate: ''
  });

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);

  // Calculate marketing metrics
  const totalCampaigns = mockCampaigns.length;
  const activeCampaigns = mockCampaigns.filter(c => c.status === 'active').length;
  const totalBookingsFromCampaigns = mockCampaigns.reduce((sum, c) => sum + c.bookingsGenerated, 0);
  const totalRevenueFromCampaigns = mockCampaigns.reduce((sum, c) => sum + c.revenue, 0);
  const averageRating = mockReviews.reduce((sum, r) => sum + r.rating, 0) / mockReviews.length;
  const totalReviews = mockReviews.length;
  const publicReviews = mockReviews.filter(r => r.isPublic).length;

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      scheduled: 'bg-blue-100 text-blue-800',
      paused: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-gray-100 text-gray-800'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      discount: DollarSign,
      promotion: Megaphone,
      seasonal: Calendar,
      referral: Users
    };
    return icons[type as keyof typeof icons] || Megaphone;
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const handleCreateCampaign = () => {
    // In a real app, this would make an API call
    console.log('Creating campaign:', newCampaign);
    setIsCreateCampaignOpen(false);
    setNewCampaign({
      name: '',
      type: 'discount',
      discount: '',
      startDate: '',
      endDate: ''
    });
  };

  const marketingStats = [
    {
      title: 'Active Campaigns',
      value: activeCampaigns.toString(),
      change: '+2',
      trend: 'up',
      icon: Megaphone,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Campaign Revenue',
      value: `₹${(totalRevenueFromCampaigns / 1000).toFixed(0)}K`,
      change: '+15.3%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Average Rating',
      value: `${averageRating.toFixed(1)}/5.0`,
      change: '+0.2',
      trend: 'up',
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Total Reviews',
      value: totalReviews.toString(),
      change: '+8',
      trend: 'up',
      icon: MessageSquare,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Marketing & Promotions</h1>
          <p className="text-muted-foreground">
            Manage campaigns, reviews, and marketing analytics
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Camera className="mr-2 h-4 w-4" />
            Update Photos
          </Button>
          <Dialog open={isCreateCampaignOpen} onOpenChange={setIsCreateCampaignOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Campaign
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Campaign</DialogTitle>
                <DialogDescription>
                  Launch a new promotional campaign for your hostel
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Campaign Name</label>
                  <Input
                    value={newCampaign.name}
                    onChange={(e) => setNewCampaign({...newCampaign, name: e.target.value})}
                    placeholder="e.g., Spring Break Special"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Campaign Type</label>
                    <select 
                      className="w-full p-2 border rounded-md"
                      value={newCampaign.type}
                      onChange={(e) => setNewCampaign({...newCampaign, type: e.target.value as Campaign['type']})}
                    >
                      <option value="discount">Discount</option>
                      <option value="promotion">Promotion</option>
                      <option value="seasonal">Seasonal</option>
                      <option value="referral">Referral</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Discount (%)</label>
                    <Input
                      type="number"
                      value={newCampaign.discount}
                      onChange={(e) => setNewCampaign({...newCampaign, discount: e.target.value})}
                      placeholder="e.g., 20"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Start Date</label>
                    <Input
                      type="date"
                      value={newCampaign.startDate}
                      onChange={(e) => setNewCampaign({...newCampaign, startDate: e.target.value})}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">End Date</label>
                    <Input
                      type="date"
                      value={newCampaign.endDate}
                      onChange={(e) => setNewCampaign({...newCampaign, endDate: e.target.value})}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateCampaignOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleCreateCampaign}>
                  <Save className="mr-2 h-4 w-4" />
                  Create Campaign
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Marketing Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {marketingStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('campaigns')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'campaigns'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Megaphone className="inline-block mr-2 h-4 w-4" />
            Campaigns
          </button>
          <button
            onClick={() => setActiveTab('reviews')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'reviews'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Star className="inline-block mr-2 h-4 w-4" />
            Reviews
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <BarChart3 className="inline-block mr-2 h-4 w-4" />
            Analytics
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'campaigns' && (
        <Card>
          <CardHeader>
            <CardTitle>Active Campaigns</CardTitle>
            <CardDescription>
              Manage your promotional campaigns and track their performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campaign</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Discount</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockCampaigns.map((campaign) => {
                    const TypeIcon = getTypeIcon(campaign.type);
                    const ctr = campaign.impressions > 0 ? ((campaign.clicks / campaign.impressions) * 100).toFixed(1) : '0';
                    return (
                      <TableRow key={campaign.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-2">
                            <TypeIcon className="h-4 w-4 text-muted-foreground" />
                            <span>{campaign.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="capitalize">{campaign.type}</span>
                        </TableCell>
                        <TableCell>
                          <span className="font-semibold">{campaign.discount}% OFF</span>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div>{new Date(campaign.startDate).toLocaleDateString()}</div>
                            <div className="text-muted-foreground">to {new Date(campaign.endDate).toLocaleDateString()}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-semibold">{campaign.bookingsGenerated} bookings</div>
                            <div className="text-muted-foreground">₹{(campaign.revenue / 1000).toFixed(0)}K revenue</div>
                            <div className="text-muted-foreground">{ctr}% CTR</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(campaign.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex space-x-2 justify-end">
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Share2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'reviews' && (
        <Card>
          <CardHeader>
            <CardTitle>Guest Reviews</CardTitle>
            <CardDescription>
              Manage and respond to guest reviews
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockReviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <div>
                        <div className="font-medium">{review.guestName}</div>
                        <div className="flex items-center space-x-2">
                          <div className="flex">
                            {getRatingStars(review.rating)}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.date).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {review.isPublic ? (
                        <Badge className="bg-green-100 text-green-800">Public</Badge>
                      ) : (
                        <Badge className="bg-gray-100 text-gray-800">Private</Badge>
                      )}
                    </div>
                  </div>

                  <p className="text-sm text-muted-foreground mb-3">
                    {review.comment}
                  </p>

                  {review.response ? (
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-sm font-medium text-blue-900 mb-1">Your Response:</div>
                      <p className="text-sm text-blue-800">{review.response}</p>
                    </div>
                  ) : (
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Respond
                      </Button>
                      {!review.isPublic && (
                        <Button variant="outline" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          Make Public
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'analytics' && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Campaign Performance
              </CardTitle>
              <CardDescription>
                Track the effectiveness of your marketing campaigns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total Impressions</span>
                  <span className="font-semibold">
                    {mockCampaigns.reduce((sum, c) => sum + c.impressions, 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total Clicks</span>
                  <span className="font-semibold">
                    {mockCampaigns.reduce((sum, c) => sum + c.clicks, 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average CTR</span>
                  <span className="font-semibold">
                    {(
                      (mockCampaigns.reduce((sum, c) => sum + c.clicks, 0) /
                       mockCampaigns.reduce((sum, c) => sum + c.impressions, 0)) * 100
                    ).toFixed(1)}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Conversion Rate</span>
                  <span className="font-semibold">
                    {(
                      (totalBookingsFromCampaigns /
                       mockCampaigns.reduce((sum, c) => sum + c.clicks, 0)) * 100
                    ).toFixed(1)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Review Analytics
              </CardTitle>
              <CardDescription>
                Insights from guest feedback and ratings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Rating</span>
                  <div className="flex items-center space-x-2">
                    <div className="flex">
                      {getRatingStars(Math.round(averageRating))}
                    </div>
                    <span className="font-semibold">{averageRating.toFixed(1)}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Total Reviews</span>
                  <span className="font-semibold">{totalReviews}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Public Reviews</span>
                  <span className="font-semibold">{publicReviews}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Response Rate</span>
                  <span className="font-semibold">
                    {((mockReviews.filter(r => r.response).length / totalReviews) * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
