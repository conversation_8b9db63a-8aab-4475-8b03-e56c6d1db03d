import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Users, 
  Search, 
  MoreHorizontal,
  Eye,
  MessageSquare,
  Calendar,
  User,
  Building2,
  Bed,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  Mail,
  Phone,
  MapPin,
  LogIn,
  LogOut,
  Send
} from 'lucide-react';
import { mockBookings, mockUsers, mockHostels, getUserById, getHostelById } from '@/data/mockData';

export const GuestManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'checking_in' | 'checking_out' | 'issues'>('all');
  const [selectedGuest, setSelectedGuest] = useState<any>(null);

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  
  // Get guests from bookings for owner's hostels
  const ownerBookings = mockBookings.filter(b => ownerHostels.some(h => h.id === b.hostelId));
  
  const guests = ownerBookings.map(booking => {
    const user = getUserById(booking.userId);
    const hostel = getHostelById(booking.hostelId);
    const checkInDate = new Date(booking.checkIn);
    const checkOutDate = new Date(booking.checkOut);
    const today = new Date();
    
    let guestStatus = 'active';
    if (today < checkInDate) guestStatus = 'checking_in';
    else if (today > checkOutDate) guestStatus = 'checking_out';
    else if (Math.random() > 0.9) guestStatus = 'issues'; // Mock issues

    // Calculate days stayed correctly
    let daysStayed = 0;
    if (today >= checkInDate) {
      const endDate = today > checkOutDate ? checkOutDate : today;
      daysStayed = Math.ceil((endDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24));
    }

    return {
      ...booking,
      user,
      hostel,
      checkInDate,
      checkOutDate,
      guestStatus,
      daysStayed: Math.max(0, daysStayed),
      totalDays: Math.ceil((checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)),
      hasIssues: guestStatus === 'issues',
      lastActivity: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // Random last activity within 7 days
    };
  }).filter(guest => guest.user);

  // Filter guests based on search and status
  const filteredGuests = guests.filter(guest => {
    const matchesSearch = guest.user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guest.user?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guest.hostel?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guest.bedNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || guest.guestStatus === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      checking_in: 'bg-blue-100 text-blue-800',
      checking_out: 'bg-orange-100 text-orange-800',
      issues: 'bg-red-100 text-red-800'
    };
    const icons = {
      active: CheckCircle,
      checking_in: LogIn,
      checking_out: LogOut,
      issues: AlertTriangle
    };
    const Icon = icons[status as keyof typeof icons] || CheckCircle;
    const label = status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {label}
      </Badge>
    );
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Calculate statistics
  const totalGuests = guests.length;
  const activeGuests = guests.filter(g => g.guestStatus === 'active').length;
  const checkingInToday = guests.filter(g => g.guestStatus === 'checking_in').length;
  const checkingOutToday = guests.filter(g => g.guestStatus === 'checking_out').length;
  const guestsWithIssues = guests.filter(g => g.hasIssues).length;

  const guestStats = [
    {
      title: 'Total Guests',
      value: totalGuests.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Residents',
      value: activeGuests.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Checking In',
      value: checkingInToday.toString(),
      icon: LogIn,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Issues',
      value: guestsWithIssues.toString(),
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Guest Management</h1>
          <p className="text-muted-foreground">
            Manage current residents, check-ins, check-outs, and guest communications
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <MessageSquare className="mr-2 h-4 w-4" />
            Send Announcement
          </Button>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Check-in Schedule
          </Button>
        </div>
      </div>

      {/* Guest Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {guestStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Guest Management Table */}
      <Card>
        <CardHeader>
          <CardTitle>Current Guests</CardTitle>
          <CardDescription>
            View and manage all guests across your properties
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by guest name, email, hostel, or room..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter.replace('_', ' ')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('active')}>Active</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('checking_in')}>Checking In</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('checking_out')}>Checking Out</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('issues')}>Issues</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Guests Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Guest</TableHead>
                  <TableHead>Property & Room</TableHead>
                  <TableHead>Check-in/Check-out</TableHead>
                  <TableHead>Stay Duration</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Activity</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredGuests.map((guest) => (
                  <TableRow key={`${guest.id}-${guest.userId}`}>
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={guest.user?.avatar} alt={guest.user?.name} />
                          <AvatarFallback>
                            {guest.user ? getUserInitials(guest.user.name) : 'G'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{guest.user?.name}</div>
                          <div className="text-sm text-muted-foreground">{guest.user?.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="flex items-center space-x-1">
                          <Building2 className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm font-medium">{guest.hostel?.name}</span>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Bed className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">Room {guest.bedNumber}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="flex items-center space-x-1">
                          <LogIn className="h-3 w-3 text-green-600" />
                          <span>{guest.checkInDate.toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <LogOut className="h-3 w-3 text-red-600" />
                          <span>{guest.checkOutDate.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div className="font-medium">{guest.daysStayed} / {guest.totalDays} days</div>
                        <div className="text-muted-foreground">
                          {Math.round((guest.daysStayed / guest.totalDays) * 100)}% complete
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(guest.guestStatus)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {guest.lastActivity.toLocaleDateString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <Dialog>
                            <DialogTrigger asChild>
                              <DropdownMenuItem onSelect={(e) => {
                                e.preventDefault();
                                setSelectedGuest(guest);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                            </DialogTrigger>
                          </Dialog>
                          <DropdownMenuItem>
                            <MessageSquare className="mr-2 h-4 w-4" />
                            Send Message
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Mail className="mr-2 h-4 w-4" />
                            Send Email
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {guest.hasIssues && (
                            <DropdownMenuItem className="text-red-600">
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              Report Issue
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredGuests.length === 0 && (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No guests found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Guest Details Dialog */}
      {selectedGuest && (
        <Dialog open={!!selectedGuest} onOpenChange={() => setSelectedGuest(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Guest Details
              </DialogTitle>
              <DialogDescription>
                Detailed information about {selectedGuest.user?.name}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <div className="mt-1 text-sm">{selectedGuest.user?.name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <div className="mt-1 text-sm">{selectedGuest.user?.email}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Phone</label>
                  <div className="mt-1 text-sm">{selectedGuest.user?.phone}</div>
                </div>
                <div>
                  <label className="text-sm font-medium">Status</label>
                  <div className="mt-1">
                    {getStatusBadge(selectedGuest.guestStatus)}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Property</label>
                  <div className="mt-1 text-sm">{selectedGuest.hostel?.name}</div>
                </div>
                <div>
                  <label className="text-sm font-medium">Room</label>
                  <div className="mt-1 text-sm">{selectedGuest.bedNumber}</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Check-in Date</label>
                  <div className="mt-1 text-sm">{selectedGuest.checkInDate.toLocaleDateString()}</div>
                </div>
                <div>
                  <label className="text-sm font-medium">Check-out Date</label>
                  <div className="mt-1 text-sm">{selectedGuest.checkOutDate.toLocaleDateString()}</div>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedGuest(null)}>
                Close
              </Button>
              <Button>
                <Send className="mr-2 h-4 w-4" />
                Send Message
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
