import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  ClipboardList, 
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Play,
  Pause,
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  Timer,
  User,
  Wrench,
  UserCheck,
  Coffee,
  FileText,
  MessageSquare,
  Camera,
  Upload,
  Star,
  TrendingUp,
  Target,
  Award
} from 'lucide-react';

// Extended task interface with more details
interface Task {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'on_hold';
  dueDate: string;
  category: 'maintenance' | 'guest_service' | 'cleaning' | 'admin' | 'security';
  assignedBy: string;
  estimatedTime: number;
  actualTime?: number;
  startTime?: string;
  completedTime?: string;
  notes?: string;
  attachments?: string[];
  location?: string;
  guestRoom?: string;
}

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Fix AC in Room 201',
    description: 'Air conditioning unit not cooling properly. Guest complaint received.',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-25T14:00:00Z',
    category: 'maintenance',
    assignedBy: 'Manager Smith',
    estimatedTime: 60,
    actualTime: 45,
    startTime: '2024-01-25T10:30:00Z',
    location: 'Room 201',
    guestRoom: '201',
    notes: 'Checked filters, need to replace compressor'
  },
  {
    id: '2',
    title: 'Guest Check-in Assistance',
    description: 'Help new guest with check-in process and room orientation',
    priority: 'medium',
    status: 'completed',
    dueDate: '2024-01-25T11:00:00Z',
    category: 'guest_service',
    assignedBy: 'Front Desk',
    estimatedTime: 30,
    actualTime: 25,
    startTime: '2024-01-25T10:45:00Z',
    completedTime: '2024-01-25T11:10:00Z',
    location: 'Reception',
    notes: 'Guest was very satisfied with the service'
  },
  {
    id: '3',
    title: 'Deep Clean Common Area',
    description: 'Thorough cleaning of the common lounge area including furniture',
    priority: 'medium',
    status: 'pending',
    dueDate: '2024-01-25T16:00:00Z',
    category: 'cleaning',
    assignedBy: 'Supervisor Johnson',
    estimatedTime: 90,
    location: 'Common Lounge'
  },
  {
    id: '4',
    title: 'Update Inventory Records',
    description: 'Update cleaning supplies inventory in the management system',
    priority: 'low',
    status: 'pending',
    dueDate: '2024-01-26T10:00:00Z',
    category: 'admin',
    assignedBy: 'Manager Smith',
    estimatedTime: 45,
    location: 'Storage Room'
  },
  {
    id: '5',
    title: 'Security Round Check',
    description: 'Complete evening security round and log any issues',
    priority: 'high',
    status: 'on_hold',
    dueDate: '2024-01-25T20:00:00Z',
    category: 'security',
    assignedBy: 'Security Head',
    estimatedTime: 30,
    location: 'All Floors'
  }
];

export const TaskManagement: React.FC = () => {
  const [tasks, setTasks] = useState(mockTasks);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed' | 'on_hold'>('all');
  const [priorityFilter, setPriorityFilter] = useState<'all' | 'low' | 'medium' | 'high'>('all');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'maintenance' | 'guest_service' | 'cleaning' | 'admin' | 'security'>('all');
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [taskUpdate, setTaskUpdate] = useState({
    status: '',
    notes: '',
    actualTime: ''
  });

  // Filter tasks
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.location?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'all' || task.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  // Calculate statistics
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === 'completed').length;
  const inProgressTasks = tasks.filter(t => t.status === 'in_progress').length;
  const pendingTasks = tasks.filter(t => t.status === 'pending').length;
  const overdueTasks = tasks.filter(t => new Date(t.dueDate) < new Date() && t.status !== 'completed').length;

  const taskStats = [
    {
      title: 'Total Tasks',
      value: totalTasks.toString(),
      change: '+3',
      trend: 'up',
      icon: ClipboardList,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Completed',
      value: completedTasks.toString(),
      change: '+2',
      trend: 'up',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'In Progress',
      value: inProgressTasks.toString(),
      change: '0',
      trend: 'neutral',
      icon: Timer,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Overdue',
      value: overdueTasks.toString(),
      change: '-1',
      trend: 'down',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-gray-100 text-gray-800',
      in_progress: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      on_hold: 'bg-yellow-100 text-yellow-800'
    };
    const icons = {
      pending: Clock,
      in_progress: Timer,
      completed: CheckCircle,
      on_hold: Pause
    };
    const Icon = icons[status as keyof typeof icons] || Clock;
    return (
      <Badge className={`${variants[status as keyof typeof variants]} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants]}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      maintenance: Wrench,
      guest_service: UserCheck,
      cleaning: Coffee,
      admin: FileText,
      security: AlertTriangle
    };
    return icons[category as keyof typeof icons] || ClipboardList;
  };

  const startTask = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'in_progress', startTime: new Date().toISOString() }
        : task
    ));
  };

  const completeTask = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'completed', completedTime: new Date().toISOString() }
        : task
    ));
  };

  const handleUpdateTask = () => {
    if (selectedTask) {
      setTasks(prev => prev.map(task => 
        task.id === selectedTask.id 
          ? { 
              ...task, 
              status: taskUpdate.status as any || task.status,
              notes: taskUpdate.notes || task.notes,
              actualTime: taskUpdate.actualTime ? parseInt(taskUpdate.actualTime) : task.actualTime
            }
          : task
      ));
      setIsUpdateDialogOpen(false);
      setSelectedTask(null);
      setTaskUpdate({ status: '', notes: '', actualTime: '' });
    }
  };

  const openUpdateDialog = (task: Task) => {
    setSelectedTask(task);
    setTaskUpdate({
      status: task.status,
      notes: task.notes || '',
      actualTime: task.actualTime?.toString() || ''
    });
    setIsUpdateDialogOpen(true);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status !== 'completed';
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Task Management</h1>
          <p className="text-muted-foreground">
            Manage your assigned tasks and track progress
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <MessageSquare className="mr-2 h-4 w-4" />
            Request Help
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Report Issue
          </Button>
        </div>
      </div>

      {/* Task Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {taskStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <span className={
                  stat.trend === 'up' ? 'text-green-600' : 
                  stat.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                }>
                  {stat.change}
                </span>
                <span className="ml-1">from yesterday</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Task Management */}
      <Card>
        <CardHeader>
          <CardTitle>My Tasks</CardTitle>
          <CardDescription>
            View and manage your assigned tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={(value: any) => setPriorityFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={(value: any) => setCategoryFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="guest_service">Guest Service</SelectItem>
                <SelectItem value="cleaning">Cleaning</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="security">Security</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Tasks Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Task</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Time</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTasks.map((task) => {
                  const CategoryIcon = getCategoryIcon(task.category);
                  const overdue = isOverdue(task.dueDate, task.status);
                  
                  return (
                    <TableRow key={task.id} className={overdue ? 'bg-red-50' : ''}>
                      <TableCell>
                        <div>
                          <div className="font-medium flex items-center">
                            {task.title}
                            {overdue && <AlertTriangle className="ml-2 h-4 w-4 text-red-500" />}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {task.description}
                          </div>
                          {task.location && (
                            <div className="text-sm text-muted-foreground">
                              📍 {task.location}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <CategoryIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="capitalize">{task.category.replace('_', ' ')}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(task.priority)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(task.status)}
                      </TableCell>
                      <TableCell>
                        <div className={overdue ? 'text-red-600 font-medium' : ''}>
                          {new Date(task.dueDate).toLocaleDateString()}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(task.dueDate).toLocaleTimeString('en-US', { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>Est: {formatDuration(task.estimatedTime)}</div>
                          {task.actualTime && (
                            <div className="text-muted-foreground">
                              Actual: {formatDuration(task.actualTime)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end space-x-2">
                          {task.status === 'pending' && (
                            <Button size="sm" onClick={() => startTask(task.id)}>
                              <Play className="mr-2 h-4 w-4" />
                              Start
                            </Button>
                          )}
                          {task.status === 'in_progress' && (
                            <Button size="sm" onClick={() => completeTask(task.id)}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Complete
                            </Button>
                          )}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openUpdateDialog(task)}>
                                <FileText className="mr-2 h-4 w-4" />
                                Update Task
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Camera className="mr-2 h-4 w-4" />
                                Add Photo
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <MessageSquare className="mr-2 h-4 w-4" />
                                Add Comment
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Update Task Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Update Task</DialogTitle>
            <DialogDescription>
              Update task status, add notes, and record actual time spent
            </DialogDescription>
          </DialogHeader>
          {selectedTask && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{selectedTask.title}</h4>
                <p className="text-sm text-muted-foreground">{selectedTask.description}</p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={taskUpdate.status} onValueChange={(value) => setTaskUpdate({...taskUpdate, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Actual Time Spent (minutes)</label>
                <Input
                  type="number"
                  value={taskUpdate.actualTime}
                  onChange={(e) => setTaskUpdate({...taskUpdate, actualTime: e.target.value})}
                  placeholder="Enter time in minutes"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  value={taskUpdate.notes}
                  onChange={(e) => setTaskUpdate({...taskUpdate, notes: e.target.value})}
                  placeholder="Add any notes or comments about this task..."
                  rows={4}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateTask}>
              Update Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
