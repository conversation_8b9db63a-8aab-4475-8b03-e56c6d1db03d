import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Building2, 
  Search, 
  MapPin,
  Star,
  Bed,
  Wifi,
  Car,
  Coffee,
  Shield,
  Filter,
  Heart,
  Eye,
  DollarSign
} from 'lucide-react';
import { mockHostels } from '@/data/mockData';

export const HostelSearch: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [priceRange, setPriceRange] = useState<'all' | 'budget' | 'mid' | 'premium'>('all');
  const [sortBy, setSortBy] = useState<'rating' | 'price' | 'distance'>('rating');

  const filteredHostels = mockHostels
    .filter(hostel => {
      const matchesSearch = hostel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           hostel.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesLocation = !locationFilter || 
                             hostel.city.toLowerCase().includes(locationFilter.toLowerCase()) ||
                             hostel.state.toLowerCase().includes(locationFilter.toLowerCase());
      
      let matchesPrice = true;
      if (priceRange === 'budget') matchesPrice = hostel.pricePerBed <= 2000;
      else if (priceRange === 'mid') matchesPrice = hostel.pricePerBed > 2000 && hostel.pricePerBed <= 4000;
      else if (priceRange === 'premium') matchesPrice = hostel.pricePerBed > 4000;
      
      return matchesSearch && matchesLocation && matchesPrice && hostel.status === 'active';
    })
    .sort((a, b) => {
      if (sortBy === 'rating') return b.rating - a.rating;
      if (sortBy === 'price') return a.pricePerBed - b.pricePerBed;
      return 0; // distance would require coordinates
    });

  const getAmenityIcon = (amenity: string) => {
    const iconMap: { [key: string]: any } = {
      'WiFi': Wifi,
      'Parking': Car,
      'Cafeteria': Coffee,
      'Security': Shield,
    };
    return iconMap[amenity] || Building2;
  };

  const getPriceCategory = (price: number) => {
    if (price <= 2000) return { label: 'Budget', color: 'bg-green-100 text-green-800' };
    if (price <= 4000) return { label: 'Mid-range', color: 'bg-blue-100 text-blue-800' };
    return { label: 'Premium', color: 'bg-purple-100 text-purple-800' };
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Find Hostels</h1>
          <p className="text-muted-foreground">
            Discover and book the perfect hostel for your stay
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          {filteredHostels.length} hostels found
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filters</CardTitle>
          <CardDescription>
            Find hostels that match your preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search hostels..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <div className="relative">
              <MapPin className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Location..."
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
                className="pl-8"
              />
            </div>
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value as any)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Prices</option>
              <option value="budget">Budget (≤₹2,000)</option>
              <option value="mid">Mid-range (₹2,001-4,000)</option>
              <option value="premium">Premium (>₹4,000)</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="rating">Sort by Rating</option>
              <option value="price">Sort by Price</option>
              <option value="distance">Sort by Distance</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Hostel Results */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredHostels.map((hostel) => {
          const priceCategory = getPriceCategory(hostel.pricePerBed);
          return (
            <Card key={hostel.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1">
                    <CardTitle className="text-lg">{hostel.name}</CardTitle>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <MapPin className="mr-1 h-3 w-3" />
                      {hostel.city}, {hostel.state}
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" className="p-1">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Rating and Price */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span className="font-medium">{hostel.rating}</span>
                    <span className="text-sm text-muted-foreground">
                      ({Math.floor(Math.random() * 100) + 50} reviews)
                    </span>
                  </div>
                  <Badge className={priceCategory.color}>
                    {priceCategory.label}
                  </Badge>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {hostel.description}
                </p>

                {/* Availability */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Bed className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {hostel.availableBeds} beds available
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">₹{hostel.pricePerBed}</div>
                    <div className="text-xs text-muted-foreground">per night</div>
                  </div>
                </div>

                {/* Amenities */}
                <div className="space-y-2">
                  <div className="text-sm font-medium">Amenities</div>
                  <div className="flex flex-wrap gap-2">
                    {hostel.amenities.slice(0, 4).map((amenity, index) => {
                      const IconComponent = getAmenityIcon(amenity);
                      return (
                        <div key={index} className="flex items-center space-x-1 text-xs bg-muted px-2 py-1 rounded">
                          <IconComponent className="h-3 w-3" />
                          <span>{amenity}</span>
                        </div>
                      );
                    })}
                    {hostel.amenities.length > 4 && (
                      <div className="text-xs bg-muted px-2 py-1 rounded">
                        +{hostel.amenities.length - 4} more
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="mr-1 h-3 w-3" />
                    View Details
                  </Button>
                  <Button size="sm" className="flex-1">
                    Book Now
                  </Button>
                </div>

                {/* Special Offers */}
                {Math.random() > 0.7 && (
                  <div className="bg-green-50 border border-green-200 rounded-md p-2">
                    <div className="text-xs font-medium text-green-800">
                      🎉 Special Offer: 10% off for bookings over 7 days!
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredHostels.length === 0 && (
        <div className="text-center py-12">
          <Building2 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">No hostels found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search criteria or filters to find more options.
          </p>
          <Button className="mt-4" onClick={() => {
            setSearchTerm('');
            setLocationFilter('');
            setPriceRange('all');
          }}>
            Clear Filters
          </Button>
        </div>
      )}

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Search Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{filteredHostels.length}</div>
              <div className="text-sm text-muted-foreground">Hostels Found</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                ₹{filteredHostels.length > 0 ? Math.min(...filteredHostels.map(h => h.pricePerBed)) : 0}
              </div>
              <div className="text-sm text-muted-foreground">Starting Price</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {filteredHostels.reduce((sum, h) => sum + h.availableBeds, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Available Beds</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {filteredHostels.length > 0 ? (filteredHostels.reduce((sum, h) => sum + h.rating, 0) / filteredHostels.length).toFixed(1) : 0}
              </div>
              <div className="text-sm text-muted-foreground">Avg Rating</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
