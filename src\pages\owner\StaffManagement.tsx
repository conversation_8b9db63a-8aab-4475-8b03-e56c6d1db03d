import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Users, 
  Search, 
  Plus,
  MoreHorizontal,
  Eye,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  UserPlus,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  ArrowUpRight,
  Save,
  X
} from 'lucide-react';

// Mock staff data
interface StaffMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'manager' | 'receptionist' | 'housekeeping' | 'maintenance' | 'security';
  status: 'active' | 'inactive' | 'on_leave';
  joinDate: string;
  salary: number;
  performance: number; // 1-5 rating
  tasksCompleted: number;
  totalTasks: number;
  avatar?: string;
  shift: 'morning' | 'evening' | 'night';
  workDays: string[];
}

interface Task {
  id: string;
  title: string;
  description: string;
  assignedTo: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed';
  dueDate: string;
  createdDate: string;
}

const mockStaff: StaffMember[] = [
  {
    id: '1',
    name: 'Rajesh Kumar',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    role: 'manager',
    status: 'active',
    joinDate: '2023-06-15T00:00:00Z',
    salary: 35000,
    performance: 4.5,
    tasksCompleted: 45,
    totalTasks: 50,
    shift: 'morning',
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  },
  {
    id: '2',
    name: 'Priya Sharma',
    email: '<EMAIL>',
    phone: '+91 9876543211',
    role: 'receptionist',
    status: 'active',
    joinDate: '2023-08-20T00:00:00Z',
    salary: 22000,
    performance: 4.2,
    tasksCompleted: 38,
    totalTasks: 42,
    shift: 'evening',
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
  },
  {
    id: '3',
    name: 'Amit Singh',
    email: '<EMAIL>',
    phone: '+91 9876543212',
    role: 'maintenance',
    status: 'active',
    joinDate: '2023-09-10T00:00:00Z',
    salary: 25000,
    performance: 4.0,
    tasksCompleted: 28,
    totalTasks: 35,
    shift: 'morning',
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  },
  {
    id: '4',
    name: 'Sunita Devi',
    email: '<EMAIL>',
    phone: '+91 9876543213',
    role: 'housekeeping',
    status: 'on_leave',
    joinDate: '2023-07-05T00:00:00Z',
    salary: 18000,
    performance: 3.8,
    tasksCompleted: 32,
    totalTasks: 40,
    shift: 'morning',
    workDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  }
];

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Clean Common Areas',
    description: 'Deep cleaning of lobby and common areas',
    assignedTo: '4',
    priority: 'medium',
    status: 'completed',
    dueDate: '2024-01-25T18:00:00Z',
    createdDate: '2024-01-25T09:00:00Z'
  },
  {
    id: '2',
    title: 'Fix AC in Room 101',
    description: 'Repair air conditioning unit',
    assignedTo: '3',
    priority: 'high',
    status: 'in_progress',
    dueDate: '2024-01-26T16:00:00Z',
    createdDate: '2024-01-25T14:00:00Z'
  },
  {
    id: '3',
    title: 'Guest Check-in Assistance',
    description: 'Help with evening check-ins',
    assignedTo: '2',
    priority: 'low',
    status: 'pending',
    dueDate: '2024-01-26T20:00:00Z',
    createdDate: '2024-01-26T08:00:00Z'
  }
];

export const StaffManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'staff' | 'tasks' | 'schedule'>('staff');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'on_leave'>('all');
  const [isAddStaffOpen, setIsAddStaffOpen] = useState(false);
  const [newStaff, setNewStaff] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'receptionist' as StaffMember['role'],
    salary: '',
    shift: 'morning' as StaffMember['shift']
  });

  // Filter staff based on search and status
  const filteredStaff = mockStaff.filter(staff => {
    const matchesSearch = staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         staff.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         staff.role.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || staff.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      on_leave: 'bg-yellow-100 text-yellow-800'
    };
    const icons = {
      active: CheckCircle,
      inactive: XCircle,
      on_leave: Clock
    };
    const Icon = icons[status as keyof typeof icons] || CheckCircle;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    const variants = {
      manager: 'bg-purple-100 text-purple-800',
      receptionist: 'bg-blue-100 text-blue-800',
      housekeeping: 'bg-green-100 text-green-800',
      maintenance: 'bg-orange-100 text-orange-800',
      security: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[role as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </Badge>
    );
  };

  const getTaskStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleAddStaff = () => {
    // In a real app, this would make an API call
    console.log('Adding staff member:', newStaff);
    setIsAddStaffOpen(false);
    setNewStaff({
      name: '',
      email: '',
      phone: '',
      role: 'receptionist',
      salary: '',
      shift: 'morning'
    });
  };

  // Calculate statistics
  const totalStaff = mockStaff.length;
  const activeStaff = mockStaff.filter(s => s.status === 'active').length;
  const onLeaveStaff = mockStaff.filter(s => s.status === 'on_leave').length;
  const averagePerformance = mockStaff.reduce((sum, s) => sum + s.performance, 0) / mockStaff.length;
  const totalTasks = mockTasks.length;
  const completedTasks = mockTasks.filter(t => t.status === 'completed').length;
  const pendingTasks = mockTasks.filter(t => t.status === 'pending').length;

  const staffStats = [
    {
      title: 'Total Staff',
      value: totalStaff.toString(),
      change: '+2',
      trend: 'up',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Active Staff',
      value: activeStaff.toString(),
      change: '+1',
      trend: 'up',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Avg Performance',
      value: `${averagePerformance.toFixed(1)}/5.0`,
      change: '+0.2',
      trend: 'up',
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Task Completion',
      value: `${((completedTasks / totalTasks) * 100).toFixed(0)}%`,
      change: '+5%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Staff Management</h1>
          <p className="text-muted-foreground">
            Manage employees, schedules, and task assignments
          </p>
        </div>
        <Dialog open={isAddStaffOpen} onOpenChange={setIsAddStaffOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Add Staff Member
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Staff Member</DialogTitle>
              <DialogDescription>
                Add a new employee to your team
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Full Name</label>
                  <Input
                    value={newStaff.name}
                    onChange={(e) => setNewStaff({...newStaff, name: e.target.value})}
                    placeholder="Enter full name"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    type="email"
                    value={newStaff.email}
                    onChange={(e) => setNewStaff({...newStaff, email: e.target.value})}
                    placeholder="Enter email address"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Phone</label>
                  <Input
                    value={newStaff.phone}
                    onChange={(e) => setNewStaff({...newStaff, phone: e.target.value})}
                    placeholder="Enter phone number"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Role</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newStaff.role}
                    onChange={(e) => setNewStaff({...newStaff, role: e.target.value as StaffMember['role']})}
                  >
                    <option value="manager">Manager</option>
                    <option value="receptionist">Receptionist</option>
                    <option value="housekeeping">Housekeeping</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="security">Security</option>
                  </select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Monthly Salary (₹)</label>
                  <Input
                    type="number"
                    value={newStaff.salary}
                    onChange={(e) => setNewStaff({...newStaff, salary: e.target.value})}
                    placeholder="Enter salary amount"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Shift</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newStaff.shift}
                    onChange={(e) => setNewStaff({...newStaff, shift: e.target.value as StaffMember['shift']})}
                  >
                    <option value="morning">Morning (6 AM - 2 PM)</option>
                    <option value="evening">Evening (2 PM - 10 PM)</option>
                    <option value="night">Night (10 PM - 6 AM)</option>
                  </select>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddStaffOpen(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button onClick={handleAddStaff}>
                <Save className="mr-2 h-4 w-4" />
                Add Staff Member
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Staff Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {staffStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                <span className="text-green-600">{stat.change}</span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('staff')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'staff'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Users className="inline-block mr-2 h-4 w-4" />
            Staff Directory
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'tasks'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <CheckCircle className="inline-block mr-2 h-4 w-4" />
            Task Management
          </button>
          <button
            onClick={() => setActiveTab('schedule')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'schedule'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Calendar className="inline-block mr-2 h-4 w-4" />
            Schedule
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'staff' && (
        <Card>
          <CardHeader>
            <CardTitle>Staff Directory</CardTitle>
            <CardDescription>
              Manage your team members and their information
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters and Search */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search staff by name, email, or role..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Status: {statusFilter === 'all' ? 'All' : statusFilter.replace('_', ' ')}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setStatusFilter('all')}>All</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter('active')}>Active</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter('inactive')}>Inactive</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setStatusFilter('on_leave')}>On Leave</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Staff Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff Member</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Salary</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={staff.avatar} alt={staff.name} />
                            <AvatarFallback>
                              {getUserInitials(staff.name)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{staff.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Joined {new Date(staff.joinDate).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getRoleBadge(staff.role)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3 text-muted-foreground" />
                            <span>{staff.email}</span>
                          </div>
                          <div className="flex items-center space-x-1 mt-1">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span>{staff.phone}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <div className="flex">
                            {Array.from({ length: 5 }, (_, i) => (
                              <Star
                                key={i}
                                className={`h-3 w-3 ${
                                  i < Math.floor(staff.performance)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm font-medium">{staff.performance}</span>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">
                          {staff.tasksCompleted}/{staff.totalTasks} tasks completed
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-semibold">₹{staff.salary.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">per month</div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(staff.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Calendar className="mr-2 h-4 w-4" />
                              View Schedule
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove Staff
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {filteredStaff.length === 0 && (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No staff members found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Try adjusting your search criteria or add new staff members.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {activeTab === 'tasks' && (
        <Card>
          <CardHeader>
            <CardTitle>Task Management</CardTitle>
            <CardDescription>
              Assign and track tasks for your staff members
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockTasks.map((task) => {
                const assignedStaff = mockStaff.find(s => s.id === task.assignedTo);
                return (
                  <div key={task.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="font-semibold">{task.title}</div>
                        <div className="text-sm text-muted-foreground">{task.description}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getPriorityBadge(task.priority)}
                        {getTaskStatusBadge(task.status)}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {assignedStaff ? getUserInitials(assignedStaff.name) : 'N/A'}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{assignedStaff?.name || 'Unassigned'}</span>
                        </div>
                        <div className="flex items-center space-x-1 text-sm text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>Due: {new Date(task.dueDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'schedule' && (
        <Card>
          <CardHeader>
            <CardTitle>Staff Schedule</CardTitle>
            <CardDescription>
              View and manage staff work schedules
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockStaff.filter(s => s.status === 'active').map((staff) => (
                <div key={staff.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {getUserInitials(staff.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{staff.name}</div>
                        <div className="text-sm text-muted-foreground">{staff.role}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium capitalize">{staff.shift} Shift</div>
                      <div className="text-sm text-muted-foreground">
                        {staff.shift === 'morning' ? '6 AM - 2 PM' :
                         staff.shift === 'evening' ? '2 PM - 10 PM' : '10 PM - 6 AM'}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                      <Badge
                        key={day}
                        className={staff.workDays.includes(day)
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                        }
                      >
                        {day.slice(0, 3)}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
