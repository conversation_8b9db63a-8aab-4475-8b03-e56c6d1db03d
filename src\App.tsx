import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";

// Context Providers
import { AuthProvider } from "@/contexts/AuthContext";

// Layout Components
import { PublicLayout } from "@/components/layouts/PublicLayout";
import { AuthenticatedLayout } from "@/components/layouts/AuthenticatedLayout";

// Common Components
import { ErrorBoundary } from "@/components/common/ErrorBoundary";

// Route Protection
import { ProtectedRoute, PublicRoute } from "@/components/auth/ProtectedRoute";

// Page Components
import { LandingPage } from "@/components/LandingPage";
import { LoginPage } from "@/components/auth/LoginPage";
import { ForgotPasswordPage } from "@/components/auth/ForgotPasswordPage";
import { Dashboard } from "@/pages/Dashboard";
import { Unauthorized } from "@/pages/Unauthorized";
import NotFound from "./pages/NotFound";

// Super Admin Pages
import { SystemOverview } from "@/pages/admin/SystemOverview";
import { ManageHostels } from "@/pages/admin/ManageHostels";
import { UserManagement } from "@/pages/admin/UserManagement";
import { FinancialManagement } from "@/pages/admin/FinancialManagement";
import { AnalyticsReports } from "@/pages/admin/AnalyticsReports";
import { AuditLogs } from "@/pages/admin/AuditLogs";
import { ContentManagement } from "@/pages/admin/ContentManagement";
import { SystemSettings } from "@/pages/admin/SystemSettings";
import { ComplaintsManagement } from "@/pages/admin/ComplaintsManagement";

// Owner Pages
import { MyHostels } from "@/pages/owner/MyHostels";
import { EmployeeManagement } from "@/pages/owner/EmployeeManagement";
import { BookingsOverview } from "@/pages/owner/BookingsOverview";

// Employee Pages
import { ResidentsManagement } from "@/pages/employee/ResidentsManagement";
import { RoomManagement } from "@/pages/employee/RoomManagement";
import { ComplaintsHandling } from "@/pages/employee/ComplaintsHandling";

// Member Pages
import { HostelSearch } from "@/pages/member/HostelSearch";
import { MyBookings } from "@/pages/member/MyBookings";
import { ProfileManagement } from "@/pages/member/ProfileManagement";

// Route Protection Components
import { SuperAdminRoute, OwnerRoute, EmployeeRoute, MemberRoute } from "@/components/auth/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <Routes>
            {/* Public Routes */}
            <Route path="/" element={<PublicLayout />}>
              <Route index element={
                <PublicRoute>
                  <LandingPage onPanelChange={() => {}} />
                </PublicRoute>
              } />
              <Route path="about" element={<div>About Page</div>} />
              <Route path="contact" element={<div>Contact Page</div>} />
              <Route path="features" element={<div>Features Page</div>} />
              <Route path="privacy" element={<div>Privacy Policy</div>} />
              <Route path="terms" element={<div>Terms of Service</div>} />
            </Route>

            {/* Authentication Routes */}
            <Route path="/login" element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            } />
            <Route path="/forgot-password" element={
              <PublicRoute>
                <ForgotPasswordPage />
              </PublicRoute>
            } />

            {/* Protected Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Dashboard />} />
            </Route>

            {/* Super Admin Routes */}
            <Route path="/admin" element={
              <SuperAdminRoute>
                <AuthenticatedLayout />
              </SuperAdminRoute>
            }>
              <Route path="overview" element={<SystemOverview />} />
              <Route path="hostels" element={<ManageHostels />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="financial" element={<FinancialManagement />} />
              <Route path="analytics" element={<AnalyticsReports />} />
              <Route path="audit-logs" element={<AuditLogs />} />
              <Route path="content" element={<ContentManagement />} />
              <Route path="settings" element={<SystemSettings />} />
              <Route path="complaints" element={<ComplaintsManagement />} />
            </Route>

            {/* Owner Routes */}
            <Route path="/owner" element={
              <OwnerRoute>
                <AuthenticatedLayout />
              </OwnerRoute>
            }>
              <Route path="hostels" element={<MyHostels />} />
              <Route path="employees" element={<EmployeeManagement />} />
              <Route path="bookings" element={<BookingsOverview />} />
            </Route>

            {/* Employee Routes */}
            <Route path="/employee" element={
              <EmployeeRoute>
                <AuthenticatedLayout />
              </EmployeeRoute>
            }>
              <Route path="residents" element={<ResidentsManagement />} />
              <Route path="rooms" element={<RoomManagement />} />
              <Route path="complaints" element={<ComplaintsHandling />} />
            </Route>

            {/* Member Routes */}
            <Route path="/member" element={
              <MemberRoute>
                <AuthenticatedLayout />
              </MemberRoute>
            }>
              <Route path="search" element={<HostelSearch />} />
              <Route path="bookings" element={<MyBookings />} />
              <Route path="profile" element={<ProfileManagement />} />
            </Route>

            {/* Common Protected Routes */}
            <Route path="/settings" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<div>Settings Page</div>} />
            </Route>

            <Route path="/profile" element={
              <ProtectedRoute>
                <AuthenticatedLayout />
              </ProtectedRoute>
            }>
              <Route index element={<ProfileManagement />} />
            </Route>

            {/* Error Routes */}
            <Route path="/unauthorized" element={<Unauthorized />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
