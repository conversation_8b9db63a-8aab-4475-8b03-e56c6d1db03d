import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Building2,
  Users,
  UserCheck,
  User,
  LayoutDashboard,
  Settings,
  LogOut,
  ChevronUp,
  Bell,
  Search,
  DollarSign,
  BarChart3,
  Shield,
  FileText,
  MessageSquare,
  Cog,
  Calendar,
  Wrench,
  Megaphone,
  Package,
  TrendingUp,
  ClipboardList,
  CreditCard,
  HelpCircle,
  Gift,
  Heart,
  Clock,
  Timer
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { User as UserType } from '@/data/mockData';

// Navigation items based on user role
const getNavigationItems = (userRole: UserType['role']) => {
  const baseItems = [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboard,
      roles: ['superadmin', 'owner', 'employee', 'member'],
    },
  ];

  const roleSpecificItems = [
    // Super Admin items
    {
      title: 'System Overview',
      url: '/admin/overview',
      icon: LayoutDashboard,
      roles: ['superadmin'],
    },
    {
      title: 'Manage Hostels',
      url: '/admin/hostels',
      icon: Building2,
      roles: ['superadmin'],
    },
    {
      title: 'User Management',
      url: '/admin/users',
      icon: Users,
      roles: ['superadmin'],
    },
    {
      title: 'Financial Management',
      url: '/admin/financial',
      icon: DollarSign,
      roles: ['superadmin'],
    },
    {
      title: 'Analytics & Reports',
      url: '/admin/analytics',
      icon: BarChart3,
      roles: ['superadmin'],
    },
    {
      title: 'Complaints Management',
      url: '/admin/complaints',
      icon: MessageSquare,
      roles: ['superadmin'],
    },
    {
      title: 'Content Management',
      url: '/admin/content',
      icon: FileText,
      roles: ['superadmin'],
    },
    {
      title: 'Audit Logs',
      url: '/admin/audit-logs',
      icon: Shield,
      roles: ['superadmin'],
    },
    {
      title: 'System Settings',
      url: '/admin/settings',
      icon: Cog,
      roles: ['superadmin'],
    },
    
    // Owner items
    {
      title: 'My Hostels',
      url: '/owner/hostels',
      icon: Building2,
      roles: ['owner'],
    },
    {
      title: 'Revenue Dashboard',
      url: '/owner/revenue',
      icon: DollarSign,
      roles: ['owner'],
    },
    {
      title: 'Property Analytics',
      url: '/owner/analytics',
      icon: BarChart3,
      roles: ['owner'],
    },
    {
      title: 'Bookings',
      url: '/owner/bookings',
      icon: Calendar,
      roles: ['owner'],
    },
    {
      title: 'Guest Management',
      url: '/owner/guests',
      icon: Users,
      roles: ['owner'],
    },
    {
      title: 'Staff Management',
      url: '/owner/staff',
      icon: UserCheck,
      roles: ['owner'],
    },
    {
      title: 'Maintenance & Issues',
      url: '/owner/maintenance',
      icon: Wrench,
      roles: ['owner'],
    },
    {
      title: 'Marketing & Promotions',
      url: '/owner/marketing',
      icon: Megaphone,
      roles: ['owner'],
    },
    {
      title: 'Inventory Management',
      url: '/owner/inventory',
      icon: Package,
      roles: ['owner'],
    },
    {
      title: 'Reports & Insights',
      url: '/owner/reports',
      icon: TrendingUp,
      roles: ['owner'],
    },
    {
      title: 'Employees',
      url: '/owner/employees',
      icon: Settings,
      roles: ['owner'],
    },
    
    // Employee items
    {
      title: 'Work Dashboard',
      url: '/employee/dashboard',
      icon: LayoutDashboard,
      roles: ['employee'],
    },
    {
      title: 'Task Management',
      url: '/employee/tasks',
      icon: ClipboardList,
      roles: ['employee'],
    },
    {
      title: 'Residents',
      url: '/employee/residents',
      icon: Users,
      roles: ['employee'],
    },
    {
      title: 'Room Management',
      url: '/employee/rooms',
      icon: Building2,
      roles: ['employee'],
    },
    {
      title: 'Complaints',
      url: '/employee/complaints',
      icon: Bell,
      roles: ['employee'],
    },
    
    // Member items
    {
      title: 'Find Hostels',
      url: '/member/search',
      icon: Search,
      roles: ['member'],
    },
    {
      title: 'My Bookings',
      url: '/member/bookings',
      icon: Calendar,
      roles: ['member'],
    },
    {
      title: 'Personal Profile',
      url: '/member/personal-profile',
      icon: User,
      roles: ['member'],
    },
    {
      title: 'Payment History',
      url: '/member/payment-history',
      icon: CreditCard,
      roles: ['member'],
    },
    {
      title: 'Notifications',
      url: '/member/notifications',
      icon: Bell,
      roles: ['member'],
    },
    {
      title: 'Wishlist & Favorites',
      url: '/member/wishlist',
      icon: Heart,
      roles: ['member'],
    },
    {
      title: 'Referral Program',
      url: '/member/referrals',
      icon: Gift,
      roles: ['member'],
    },
    {
      title: 'Support Center',
      url: '/member/support',
      icon: HelpCircle,
      roles: ['member'],
    },
    {
      title: 'My Profile',
      url: '/member/profile',
      icon: Settings,
      roles: ['member'],
    },
  ];

  return [...baseItems, ...roleSpecificItems].filter(item =>
    item.roles.includes(userRole)
  );
};

export const AppSidebar: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  if (!user) return null;

  const navigationItems = getNavigationItems(user.role);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleDisplayName = (role: UserType['role']) => {
    const roleNames = {
      superadmin: 'Super Admin',
      owner: 'Hostel Owner',
      employee: 'Employee',
      member: 'Member',
    };
    return roleNames[role];
  };

  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <div className="flex items-center space-x-2 px-2 py-2">
          <Building2 className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold bg-gradient-primary bg-clip-text text-transparent">
            HostelHub
          </span>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const isActive = location.pathname === item.url;
                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={isActive}
                      tooltip={item.title}
                    >
                      <button
                        onClick={() => navigate(item.url)}
                        className="flex items-center space-x-2 w-full"
                      >
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </button>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  asChild
                  isActive={location.pathname === '/settings'}
                  tooltip="Settings"
                >
                  <button
                    onClick={() => navigate('/settings')}
                    className="flex items-center space-x-2 w-full"
                  >
                    <Settings className="h-4 w-4" />
                    <span>Settings</span>
                  </button>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="rounded-lg">
                      {getUserInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{user.name}</span>
                    <span className="truncate text-xs text-muted-foreground">
                      {getRoleDisplayName(user.role)}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem onClick={() => navigate('/profile')}>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => navigate('/settings')}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} className="text-destructive">
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
};
