import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Bed,
  Search,
  Plus,
  Eye,
  Edit,
  Settings,
  Users,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Wrench,
  Sparkles,
  Home
} from 'lucide-react';

export const RoomManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'available' | 'occupied' | 'maintenance' | 'cleaning'>('all');

  // Mock room data
  const rooms = [
    { id: 'R001', number: '101', type: 'Single', beds: 1, occupiedBeds: 1, status: 'occupied', lastCleaned: '2024-01-15', maintenanceIssues: 0 },
    { id: 'R002', number: '102', type: 'Double', beds: 2, occupiedBeds: 0, status: 'available', lastCleaned: '2024-01-16', maintenanceIssues: 0 },
    { id: 'R003', number: '103', type: 'Dormitory', beds: 6, occupiedBeds: 4, status: 'occupied', lastCleaned: '2024-01-14', maintenanceIssues: 1 },
    { id: 'R004', number: '104', type: 'Single', beds: 1, occupiedBeds: 0, status: 'maintenance', lastCleaned: '2024-01-10', maintenanceIssues: 2 },
    { id: 'R005', number: '105', type: 'Triple', beds: 3, occupiedBeds: 2, status: 'occupied', lastCleaned: '2024-01-16', maintenanceIssues: 0 },
    { id: 'R006', number: '106', type: 'Double', beds: 2, occupiedBeds: 0, status: 'cleaning', lastCleaned: '2024-01-15', maintenanceIssues: 0 },
    { id: 'R007', number: '201', type: 'Dormitory', beds: 8, occupiedBeds: 6, status: 'occupied', lastCleaned: '2024-01-16', maintenanceIssues: 0 },
    { id: 'R008', number: '202', type: 'Single', beds: 1, occupiedBeds: 0, status: 'available', lastCleaned: '2024-01-16', maintenanceIssues: 0 },
  ];

  const filteredRooms = rooms.filter(room => {
    const matchesSearch = room.number.includes(searchTerm) || room.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || room.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      available: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      occupied: { color: 'bg-blue-100 text-blue-800', icon: Users },
      maintenance: { color: 'bg-red-100 text-red-800', icon: AlertTriangle },
      cleaning: { color: 'bg-yellow-100 text-yellow-800', icon: Sparkles },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={config.color}>
        <config.icon className="mr-1 h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getOccupancyRate = (room: typeof rooms[0]) => {
    return ((room.occupiedBeds / room.beds) * 100).toFixed(0);
  };

  const getRoomStats = () => {
    return {
      total: rooms.length,
      available: rooms.filter(r => r.status === 'available').length,
      occupied: rooms.filter(r => r.status === 'occupied').length,
      maintenance: rooms.filter(r => r.status === 'maintenance').length,
      cleaning: rooms.filter(r => r.status === 'cleaning').length,
      totalBeds: rooms.reduce((sum, r) => sum + r.beds, 0),
      occupiedBeds: rooms.reduce((sum, r) => sum + r.occupiedBeds, 0),
    };
  };

  const stats = getRoomStats();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Room Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage all hostel rooms and beds
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add New Room
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Rooms</CardTitle>
            <Home className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              All room types
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.available}</div>
            <p className="text-xs text-muted-foreground">
              Ready for guests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupied</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.occupied}</div>
            <p className="text-xs text-muted-foreground">
              Currently in use
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.maintenance}</div>
            <p className="text-xs text-muted-foreground">
              Need repairs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
            <Bed className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{((stats.occupiedBeds / stats.totalBeds) * 100).toFixed(0)}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.occupiedBeds}/{stats.totalBeds} beds
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Room Directory */}
      <Card>
        <CardHeader>
          <CardTitle>Room Directory</CardTitle>
          <CardDescription>
            Overview of all rooms and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search rooms by number or type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Rooms</option>
              <option value="available">Available</option>
              <option value="occupied">Occupied</option>
              <option value="maintenance">Maintenance</option>
              <option value="cleaning">Cleaning</option>
            </select>
          </div>

          {/* Room Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredRooms.map((room) => (
              <Card key={room.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">Room {room.number}</CardTitle>
                      <div className="text-sm text-muted-foreground">{room.type}</div>
                    </div>
                    {getStatusBadge(room.status)}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Bed Occupancy */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Bed Occupancy</span>
                      <span>{room.occupiedBeds}/{room.beds}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${getOccupancyRate(room)}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {getOccupancyRate(room)}% occupied
                    </div>
                  </div>

                  {/* Room Details */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-muted-foreground">Total Beds</div>
                      <div className="font-medium flex items-center">
                        <Bed className="mr-1 h-3 w-3" />
                        {room.beds}
                      </div>
                    </div>
                    <div>
                      <div className="text-muted-foreground">Available</div>
                      <div className="font-medium">{room.beds - room.occupiedBeds}</div>
                    </div>
                  </div>

                  {/* Maintenance & Cleaning */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Last Cleaned</span>
                      <span>{new Date(room.lastCleaned).toLocaleDateString()}</span>
                    </div>
                    {room.maintenanceIssues > 0 && (
                      <div className="flex items-center space-x-2 text-sm">
                        <AlertTriangle className="h-3 w-3 text-red-500" />
                        <span className="text-red-600">{room.maintenanceIssues} maintenance issue(s)</span>
                      </div>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Eye className="mr-1 h-3 w-3" />
                      View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Edit className="mr-1 h-3 w-3" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>

                  {/* Status-specific Actions */}
                  {room.status === 'maintenance' && (
                    <Button variant="outline" size="sm" className="w-full text-red-600 border-red-200">
                      <Wrench className="mr-2 h-3 w-3" />
                      Schedule Maintenance
                    </Button>
                  )}
                  {room.status === 'cleaning' && (
                    <Button variant="outline" size="sm" className="w-full text-yellow-600 border-yellow-200">
                      <Sparkles className="mr-2 h-3 w-3" />
                      Mark as Cleaned
                    </Button>
                  )}
                  {room.status === 'available' && room.beds - room.occupiedBeds > 0 && (
                    <Button size="sm" className="w-full">
                      <Users className="mr-2 h-3 w-3" />
                      Assign Guest
                    </Button>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredRooms.length === 0 && (
            <div className="text-center py-12">
              <Home className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No rooms found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding your first room.'}
              </p>
              {!searchTerm && (
                <Button className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Your First Room
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
