import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Package, 
  Search, 
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownRight,
  Bed,
  Wifi,
  Coffee,
  Tv,
  AirVent,
  Save,
  X,
  ShoppingCart
} from 'lucide-react';

// Mock inventory data
interface InventoryItem {
  id: string;
  name: string;
  category: 'bedding' | 'electronics' | 'furniture' | 'amenities' | 'cleaning' | 'maintenance';
  currentStock: number;
  minStock: number;
  maxStock: number;
  unitPrice: number;
  supplier: string;
  lastRestocked: string;
  location: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock';
}

const mockInventory: InventoryItem[] = [
  {
    id: '1',
    name: 'Bed Sheets (Single)',
    category: 'bedding',
    currentStock: 45,
    minStock: 20,
    maxStock: 100,
    unitPrice: 500,
    supplier: 'Textile Suppliers Ltd',
    lastRestocked: '2024-01-20T00:00:00Z',
    location: 'Storage Room A',
    status: 'in_stock'
  },
  {
    id: '2',
    name: 'WiFi Router',
    category: 'electronics',
    currentStock: 3,
    minStock: 2,
    maxStock: 10,
    unitPrice: 2500,
    supplier: 'Tech Solutions',
    lastRestocked: '2024-01-15T00:00:00Z',
    location: 'IT Room',
    status: 'in_stock'
  },
  {
    id: '3',
    name: 'Pillows',
    category: 'bedding',
    currentStock: 8,
    minStock: 15,
    maxStock: 50,
    unitPrice: 300,
    supplier: 'Comfort Bedding Co',
    lastRestocked: '2024-01-10T00:00:00Z',
    location: 'Storage Room A',
    status: 'low_stock'
  },
  {
    id: '4',
    name: 'Cleaning Supplies',
    category: 'cleaning',
    currentStock: 0,
    minStock: 10,
    maxStock: 30,
    unitPrice: 150,
    supplier: 'Clean Pro Supplies',
    lastRestocked: '2024-01-05T00:00:00Z',
    location: 'Cleaning Closet',
    status: 'out_of_stock'
  },
  {
    id: '5',
    name: 'Study Tables',
    category: 'furniture',
    currentStock: 12,
    minStock: 5,
    maxStock: 25,
    unitPrice: 3500,
    supplier: 'Furniture World',
    lastRestocked: '2024-01-18T00:00:00Z',
    location: 'Storage Room B',
    status: 'in_stock'
  }
];

export const InventoryManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'bedding' | 'electronics' | 'furniture' | 'amenities' | 'cleaning' | 'maintenance'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'in_stock' | 'low_stock' | 'out_of_stock'>('all');
  const [isAddItemOpen, setIsAddItemOpen] = useState(false);
  const [newItem, setNewItem] = useState({
    name: '',
    category: 'bedding' as InventoryItem['category'],
    currentStock: '',
    minStock: '',
    maxStock: '',
    unitPrice: '',
    supplier: '',
    location: ''
  });

  // Filter inventory based on search and filters
  const filteredInventory = mockInventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      in_stock: 'bg-green-100 text-green-800',
      low_stock: 'bg-yellow-100 text-yellow-800',
      out_of_stock: 'bg-red-100 text-red-800'
    };
    const icons = {
      in_stock: CheckCircle,
      low_stock: AlertTriangle,
      out_of_stock: XCircle
    };
    const Icon = icons[status as keyof typeof icons] || CheckCircle;
    return (
      <Badge className={`${variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').charAt(0).toUpperCase() + status.replace('_', ' ').slice(1)}
      </Badge>
    );
  };

  const getCategoryIcon = (category: string) => {
    const icons = {
      bedding: Bed,
      electronics: Wifi,
      furniture: Coffee,
      amenities: Tv,
      cleaning: Package,
      maintenance: AirVent
    };
    return icons[category as keyof typeof icons] || Package;
  };

  const getCategoryLabel = (category: string) => {
    const labels = {
      bedding: 'Bedding',
      electronics: 'Electronics',
      furniture: 'Furniture',
      amenities: 'Amenities',
      cleaning: 'Cleaning',
      maintenance: 'Maintenance'
    };
    return labels[category as keyof typeof labels] || category;
  };

  const handleAddItem = () => {
    // In a real app, this would make an API call
    console.log('Adding inventory item:', newItem);
    setIsAddItemOpen(false);
    setNewItem({
      name: '',
      category: 'bedding',
      currentStock: '',
      minStock: '',
      maxStock: '',
      unitPrice: '',
      supplier: '',
      location: ''
    });
  };

  // Calculate statistics
  const totalItems = mockInventory.length;
  const inStockItems = mockInventory.filter(i => i.status === 'in_stock').length;
  const lowStockItems = mockInventory.filter(i => i.status === 'low_stock').length;
  const outOfStockItems = mockInventory.filter(i => i.status === 'out_of_stock').length;
  const totalValue = mockInventory.reduce((sum, i) => sum + (i.currentStock * i.unitPrice), 0);

  const inventoryStats = [
    {
      title: 'Total Items',
      value: totalItems.toString(),
      change: '+3',
      trend: 'up',
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'In Stock',
      value: inStockItems.toString(),
      change: '+2',
      trend: 'up',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Low Stock',
      value: lowStockItems.toString(),
      change: '+1',
      trend: 'up',
      icon: AlertTriangle,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Total Value',
      value: `₹${(totalValue / 1000).toFixed(0)}K`,
      change: '+8.5%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory Management</h1>
          <p className="text-muted-foreground">
            Track room amenities, supplies, and equipment
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <ShoppingCart className="mr-2 h-4 w-4" />
            Reorder Items
          </Button>
          <Dialog open={isAddItemOpen} onOpenChange={setIsAddItemOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add Inventory Item</DialogTitle>
                <DialogDescription>
                  Add a new item to your inventory
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Item Name</label>
                    <Input
                      value={newItem.name}
                      onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                      placeholder="Enter item name"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Category</label>
                    <select 
                      className="w-full p-2 border rounded-md"
                      value={newItem.category}
                      onChange={(e) => setNewItem({...newItem, category: e.target.value as InventoryItem['category']})}
                    >
                      <option value="bedding">Bedding</option>
                      <option value="electronics">Electronics</option>
                      <option value="furniture">Furniture</option>
                      <option value="amenities">Amenities</option>
                      <option value="cleaning">Cleaning</option>
                      <option value="maintenance">Maintenance</option>
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium">Current Stock</label>
                    <Input
                      type="number"
                      value={newItem.currentStock}
                      onChange={(e) => setNewItem({...newItem, currentStock: e.target.value})}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Min Stock</label>
                    <Input
                      type="number"
                      value={newItem.minStock}
                      onChange={(e) => setNewItem({...newItem, minStock: e.target.value})}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Max Stock</label>
                    <Input
                      type="number"
                      value={newItem.maxStock}
                      onChange={(e) => setNewItem({...newItem, maxStock: e.target.value})}
                      placeholder="0"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Unit Price (₹)</label>
                    <Input
                      type="number"
                      value={newItem.unitPrice}
                      onChange={(e) => setNewItem({...newItem, unitPrice: e.target.value})}
                      placeholder="0"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Location</label>
                    <Input
                      value={newItem.location}
                      onChange={(e) => setNewItem({...newItem, location: e.target.value})}
                      placeholder="Storage location"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium">Supplier</label>
                  <Input
                    value={newItem.supplier}
                    onChange={(e) => setNewItem({...newItem, supplier: e.target.value})}
                    placeholder="Supplier name"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddItemOpen(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleAddItem}>
                  <Save className="mr-2 h-4 w-4" />
                  Add Item
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Inventory Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {inventoryStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Items</CardTitle>
          <CardDescription>
            Manage your hostel's inventory and supplies
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search items by name, supplier, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Category: {categoryFilter === 'all' ? 'All' : getCategoryLabel(categoryFilter)}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setCategoryFilter('all')}>All Categories</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('bedding')}>Bedding</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('electronics')}>Electronics</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('furniture')}>Furniture</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('amenities')}>Amenities</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('cleaning')}>Cleaning</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setCategoryFilter('maintenance')}>Maintenance</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter.replace('_', ' ')}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All Statuses</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('in_stock')}>In Stock</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('low_stock')}>Low Stock</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('out_of_stock')}>Out of Stock</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Inventory Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Stock Level</TableHead>
                  <TableHead>Unit Price</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventory.map((item) => {
                  const CategoryIcon = getCategoryIcon(item.category);
                  const stockPercentage = (item.currentStock / item.maxStock) * 100;
                  return (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{item.name}</div>
                          <div className="text-sm text-muted-foreground">
                            Supplier: {item.supplier}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <CategoryIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{getCategoryLabel(item.category)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium">{item.currentStock}</span>
                            <span className="text-muted-foreground">/ {item.maxStock}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${
                                stockPercentage > 50 ? 'bg-green-500' :
                                stockPercentage > 20 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${Math.min(stockPercentage, 100)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Min: {item.minStock}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-semibold">₹{item.unitPrice.toLocaleString()}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-semibold">
                          ₹{(item.currentStock * item.unitPrice).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(item.status)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{item.location}</div>
                        <div className="text-xs text-muted-foreground">
                          Last restocked: {new Date(item.lastRestocked).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Item
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <ShoppingCart className="mr-2 h-4 w-4" />
                              Restock
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {item.status === 'low_stock' && (
                              <DropdownMenuItem className="text-orange-600">
                                <AlertTriangle className="mr-2 h-4 w-4" />
                                Urgent Restock
                              </DropdownMenuItem>
                            )}
                            {item.status === 'out_of_stock' && (
                              <DropdownMenuItem className="text-red-600">
                                <XCircle className="mr-2 h-4 w-4" />
                                Emergency Order
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredInventory.length === 0 && (
            <div className="text-center py-8">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No inventory items found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or add new items.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
