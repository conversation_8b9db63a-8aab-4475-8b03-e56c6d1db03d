import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  FileText, 
  Search, 
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Megaphone,
  Shield,
  BookOpen,
  Bell,
  Globe,
  Filter,
  Save,
  X
} from 'lucide-react';

// Mock content data
interface ContentItem {
  id: string;
  title: string;
  type: 'announcement' | 'policy' | 'faq' | 'terms' | 'privacy';
  content: string;
  status: 'published' | 'draft' | 'archived';
  priority: 'low' | 'medium' | 'high';
  createdBy: string;
  createdDate: string;
  lastModified: string;
  views: number;
}

const mockContent: ContentItem[] = [
  {
    id: '1',
    title: 'Platform Maintenance Scheduled',
    type: 'announcement',
    content: 'We will be performing scheduled maintenance on our platform from 2:00 AM to 4:00 AM IST on Sunday, January 28th. During this time, the platform may be temporarily unavailable.',
    status: 'published',
    priority: 'high',
    createdBy: 'Admin User',
    createdDate: '2024-01-25T10:00:00Z',
    lastModified: '2024-01-25T10:00:00Z',
    views: 1250
  },
  {
    id: '2',
    title: 'Updated Privacy Policy',
    type: 'policy',
    content: 'We have updated our privacy policy to better protect your personal information and comply with the latest data protection regulations.',
    status: 'published',
    priority: 'medium',
    createdBy: 'Admin User',
    createdDate: '2024-01-20T14:30:00Z',
    lastModified: '2024-01-22T09:15:00Z',
    views: 890
  },
  {
    id: '3',
    title: 'How to Book a Hostel',
    type: 'faq',
    content: 'Step-by-step guide on how to search, compare, and book hostels on our platform.',
    status: 'published',
    priority: 'low',
    createdBy: 'Admin User',
    createdDate: '2024-01-15T11:20:00Z',
    lastModified: '2024-01-18T16:45:00Z',
    views: 2340
  },
  {
    id: '4',
    title: 'New Payment Options Available',
    type: 'announcement',
    content: 'We are excited to announce that we now support additional payment methods including digital wallets and bank transfers.',
    status: 'draft',
    priority: 'medium',
    createdBy: 'Admin User',
    createdDate: '2024-01-24T09:00:00Z',
    lastModified: '2024-01-24T15:30:00Z',
    views: 0
  },
  {
    id: '5',
    title: 'Terms of Service',
    type: 'terms',
    content: 'Complete terms and conditions for using our hostel booking platform.',
    status: 'published',
    priority: 'high',
    createdBy: 'Admin User',
    createdDate: '2024-01-01T00:00:00Z',
    lastModified: '2024-01-20T12:00:00Z',
    views: 5670
  }
];

export const ContentManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'announcement' | 'policy' | 'faq' | 'terms' | 'privacy'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft' | 'archived'>('all');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newContent, setNewContent] = useState({
    title: '',
    type: 'announcement' as ContentItem['type'],
    content: '',
    priority: 'medium' as ContentItem['priority']
  });

  // Filter content based on search and filters
  const filteredContent = mockContent.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || item.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-gray-100 text-gray-800'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-blue-100 text-blue-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const getTypeIcon = (type: string) => {
    const icons = {
      announcement: Megaphone,
      policy: Shield,
      faq: BookOpen,
      terms: FileText,
      privacy: Shield
    };
    return icons[type as keyof typeof icons] || FileText;
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      announcement: 'Announcement',
      policy: 'Policy',
      faq: 'FAQ',
      terms: 'Terms',
      privacy: 'Privacy'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const handleCreateContent = () => {
    // In a real app, this would make an API call
    console.log('Creating content:', newContent);
    setIsCreateDialogOpen(false);
    setNewContent({
      title: '',
      type: 'announcement',
      content: '',
      priority: 'medium'
    });
  };

  // Calculate statistics
  const totalContent = mockContent.length;
  const publishedContent = mockContent.filter(item => item.status === 'published').length;
  const draftContent = mockContent.filter(item => item.status === 'draft').length;
  const totalViews = mockContent.reduce((sum, item) => sum + item.views, 0);

  const contentStats = [
    {
      title: 'Total Content',
      value: totalContent.toString(),
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Published',
      value: publishedContent.toString(),
      icon: Globe,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Drafts',
      value: draftContent.toString(),
      icon: Edit,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Total Views',
      value: totalViews.toLocaleString(),
      icon: Eye,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Content Management</h1>
          <p className="text-muted-foreground">
            Manage platform content, announcements, and policies
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Content
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Content</DialogTitle>
              <DialogDescription>
                Add new content to your platform
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={newContent.title}
                  onChange={(e) => setNewContent({...newContent, title: e.target.value})}
                  placeholder="Enter content title"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Type</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newContent.type}
                    onChange={(e) => setNewContent({...newContent, type: e.target.value as ContentItem['type']})}
                  >
                    <option value="announcement">Announcement</option>
                    <option value="policy">Policy</option>
                    <option value="faq">FAQ</option>
                    <option value="terms">Terms</option>
                    <option value="privacy">Privacy</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Priority</label>
                  <select 
                    className="w-full p-2 border rounded-md"
                    value={newContent.priority}
                    onChange={(e) => setNewContent({...newContent, priority: e.target.value as ContentItem['priority']})}
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Content</label>
                <Textarea
                  value={newContent.content}
                  onChange={(e) => setNewContent({...newContent, content: e.target.value})}
                  placeholder="Enter content text"
                  rows={6}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
              <Button onClick={handleCreateContent}>
                <Save className="mr-2 h-4 w-4" />
                Create Content
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Content Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {contentStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Content Table */}
      <Card>
        <CardHeader>
          <CardTitle>Content Library</CardTitle>
          <CardDescription>
            Manage all platform content and documentation
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search content by title or content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Type: {typeFilter === 'all' ? 'All' : getTypeLabel(typeFilter)}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setTypeFilter('all')}>All Types</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTypeFilter('announcement')}>Announcements</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTypeFilter('policy')}>Policies</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTypeFilter('faq')}>FAQs</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTypeFilter('terms')}>Terms</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTypeFilter('privacy')}>Privacy</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Globe className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>All Statuses</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('published')}>Published</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('draft')}>Draft</DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('archived')}>Archived</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Content Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Content</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Views</TableHead>
                  <TableHead>Last Modified</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredContent.map((item) => {
                  const TypeIcon = getTypeIcon(item.type);
                  return (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">
                        <div>
                          <div className="font-semibold">{item.title}</div>
                          <div className="text-sm text-muted-foreground max-w-md truncate">
                            {item.content}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <TypeIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{getTypeLabel(item.type)}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(item.status)}
                      </TableCell>
                      <TableCell>
                        {getPriorityBadge(item.priority)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Eye className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{item.views.toLocaleString()}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {new Date(item.lastModified).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Content
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Content
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {item.status === 'draft' && (
                              <DropdownMenuItem>
                                <Globe className="mr-2 h-4 w-4" />
                                Publish
                              </DropdownMenuItem>
                            )}
                            {item.status === 'published' && (
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Unpublish
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredContent.length === 0 && (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No content found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or create new content.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
