import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  DollarSign, 
  Search, 
  Download,
  MoreHorizontal,
  Eye,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Wallet,
  PieChart,
  Calendar,
  Filter,
  ArrowUpRight,
  ArrowDownRight,
  Building2,
  Users
} from 'lucide-react';
import { mockPayments, mockBookings, mockHostels, getUserById, getHostelById } from '@/data/mockData';

export const FinancialManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'pending' | 'failed'>('all');
  const [methodFilter, setMethodFilter] = useState<'all' | 'card' | 'upi' | 'bank_transfer'>('all');

  // Calculate financial statistics
  const totalRevenue = mockPayments.reduce((sum, p) => sum + p.amount, 0);
  const platformRevenue = mockPayments.reduce((sum, p) => sum + p.splitDetails.platform, 0);
  const hostelOwnerRevenue = mockPayments.reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);
  const successfulPayments = mockPayments.filter(p => p.status === 'success').length;
  const pendingPayments = mockPayments.filter(p => p.status === 'pending').length;
  const failedPayments = mockPayments.filter(p => p.status === 'failed').length;
  const averageTransactionValue = totalRevenue / mockPayments.length;

  // Filter payments based on search and filters
  const filteredPayments = mockPayments.filter(payment => {
    const user = getUserById(payment.userId);
    const booking = mockBookings.find(b => b.id === payment.bookingId);
    const hostel = booking ? getHostelById(booking.hostelId) : null;
    
    const matchesSearch = user?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hostel?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || payment.status === statusFilter;
    const matchesMethod = methodFilter === 'all' || payment.method === methodFilter;
    
    return matchesSearch && matchesStatus && matchesMethod;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800',
      failed: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getMethodBadge = (method: string) => {
    const variants = {
      card: 'bg-blue-100 text-blue-800',
      upi: 'bg-purple-100 text-purple-800',
      bank_transfer: 'bg-orange-100 text-orange-800'
    };
    const labels = {
      card: 'Card',
      upi: 'UPI',
      bank_transfer: 'Bank Transfer'
    };
    return (
      <Badge className={variants[method as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {labels[method as keyof typeof labels] || method}
      </Badge>
    );
  };

  const financialStats = [
    {
      title: 'Total Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Platform Revenue',
      value: `₹${(platformRevenue / 1000).toFixed(0)}K`,
      change: '+8.2%',
      trend: 'up',
      icon: Wallet,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Avg Transaction',
      value: `₹${(averageTransactionValue / 1000).toFixed(0)}K`,
      change: '+3.1%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Success Rate',
      value: `${((successfulPayments / mockPayments.length) * 100).toFixed(1)}%`,
      change: '+2.3%',
      trend: 'up',
      icon: CreditCard,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Financial Management</h1>
          <p className="text-muted-foreground">
            Monitor payments, revenue, and financial analytics
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <PieChart className="mr-2 h-4 w-4" />
            Generate Analytics
          </Button>
        </div>
      </div>

      {/* Financial Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {financialStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Revenue Breakdown */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Revenue Split
            </CardTitle>
            <CardDescription>
              Platform vs Hostel Owner revenue distribution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium">Platform Commission</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">₹{(platformRevenue / 1000).toFixed(0)}K</div>
                  <div className="text-xs text-muted-foreground">
                    {((platformRevenue / totalRevenue) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">Hostel Owner Share</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold">₹{(hostelOwnerRevenue / 1000).toFixed(0)}K</div>
                  <div className="text-xs text-muted-foreground">
                    {((hostelOwnerRevenue / totalRevenue) * 100).toFixed(1)}%
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Methods
            </CardTitle>
            <CardDescription>
              Distribution of payment methods used
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {['upi', 'card', 'bank_transfer'].map(method => {
                const count = mockPayments.filter(p => p.method === method).length;
                const percentage = ((count / mockPayments.length) * 100).toFixed(1);
                return (
                  <div key={method} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${
                        method === 'upi' ? 'bg-purple-500' : 
                        method === 'card' ? 'bg-blue-500' : 'bg-orange-500'
                      }`}></div>
                      <span className="text-sm font-medium capitalize">
                        {method === 'bank_transfer' ? 'Bank Transfer' : method.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{count}</div>
                      <div className="text-xs text-muted-foreground">{percentage}%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Transactions</CardTitle>
          <CardDescription>
            View and manage all payment transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters and Search */}
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by user, hostel, or transaction ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  Status: {statusFilter === 'all' ? 'All' : statusFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  All Statuses
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('success')}>
                  Success
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('pending')}>
                  Pending
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('failed')}>
                  Failed
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Method: {methodFilter === 'all' ? 'All' : methodFilter}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Filter by Method</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setMethodFilter('all')}>
                  All Methods
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setMethodFilter('card')}>
                  Card
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setMethodFilter('upi')}>
                  UPI
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setMethodFilter('bank_transfer')}>
                  Bank Transfer
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Payments Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Hostel</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPayments.map((payment) => {
                  const user = getUserById(payment.userId);
                  const booking = mockBookings.find(b => b.id === payment.bookingId);
                  const hostel = booking ? getHostelById(booking.hostelId) : null;
                  return (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span>#{payment.id}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user?.name || 'Unknown User'}</div>
                          <div className="text-sm text-muted-foreground">{user?.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Building2 className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{hostel?.name || 'Unknown Hostel'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-semibold">₹{payment.amount.toLocaleString()}</div>
                          <div className="text-xs text-muted-foreground">
                            Platform: ₹{payment.splitDetails.platform.toLocaleString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getMethodBadge(payment.method)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {new Date(payment.date).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(payment.status)}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download Receipt
                            </DropdownMenuItem>
                            {payment.status === 'pending' && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  Process Payment
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {filteredPayments.length === 0 && (
            <div className="text-center py-8">
              <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
