import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Bell, 
  Settings,
  MoreHorizontal,
  Eye,
  Trash2,
  Calendar,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Info,
  Star,
  Building2,
  Clock,
  DollarSign,
  MessageSquare,
  Gift,
  Zap,
  Filter,
  MarkAsRead,
  Archive,
  BellOff
} from 'lucide-react';

// Mock notification data
interface Notification {
  id: string;
  type: 'booking' | 'payment' | 'system' | 'promotion' | 'reminder' | 'announcement';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: {
    bookingId?: string;
    amount?: number;
    hostelName?: string;
  };
}

const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'payment',
    title: 'Payment Due Reminder',
    message: 'Your monthly payment of ₹8,000 for Urban Stay Hostel is due in 3 days.',
    timestamp: '2024-01-22T10:30:00Z',
    isRead: false,
    priority: 'high',
    metadata: {
      amount: 8000,
      hostelName: 'Urban Stay Hostel'
    }
  },
  {
    id: '2',
    type: 'booking',
    title: 'Check-in Reminder',
    message: 'Your check-in at Elite Residency is scheduled for tomorrow at 2:00 PM.',
    timestamp: '2024-01-21T15:45:00Z',
    isRead: false,
    priority: 'medium',
    metadata: {
      bookingId: 'BK-001',
      hostelName: 'Elite Residency'
    }
  },
  {
    id: '3',
    type: 'system',
    title: 'Profile Verification Complete',
    message: 'Your identity documents have been verified successfully.',
    timestamp: '2024-01-20T09:15:00Z',
    isRead: true,
    priority: 'medium'
  },
  {
    id: '4',
    type: 'promotion',
    title: 'Special Offer: 20% Off',
    message: 'Get 20% off on your next booking. Use code SAVE20. Valid until Jan 31st.',
    timestamp: '2024-01-19T12:00:00Z',
    isRead: true,
    priority: 'low'
  },
  {
    id: '5',
    type: 'announcement',
    title: 'New Feature: Mobile App',
    message: 'Download our new mobile app for easier booking management on the go.',
    timestamp: '2024-01-18T08:30:00Z',
    isRead: false,
    priority: 'low'
  },
  {
    id: '6',
    type: 'reminder',
    title: 'Review Your Stay',
    message: 'How was your experience at Campus Lodge? Share your feedback.',
    timestamp: '2024-01-17T14:20:00Z',
    isRead: true,
    priority: 'low',
    metadata: {
      hostelName: 'Campus Lodge'
    }
  }
];

export const NotificationsAlerts: React.FC = () => {
  const [notifications, setNotifications] = useState(mockNotifications);
  const [filter, setFilter] = useState<'all' | 'unread' | 'booking' | 'payment' | 'system' | 'promotion'>('all');
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    bookingReminders: true,
    paymentAlerts: true,
    promotionalOffers: true,
    systemUpdates: true,
    reviewReminders: false
  });

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.isRead;
    return notification.type === filter;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const getNotificationIcon = (type: string) => {
    const icons = {
      booking: Calendar,
      payment: CreditCard,
      system: Settings,
      promotion: Gift,
      reminder: Clock,
      announcement: MessageSquare
    };
    return icons[type as keyof typeof icons] || Bell;
  };

  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'high') return 'text-red-600 bg-red-50';
    
    const colors = {
      booking: 'text-blue-600 bg-blue-50',
      payment: 'text-green-600 bg-green-50',
      system: 'text-purple-600 bg-purple-50',
      promotion: 'text-orange-600 bg-orange-50',
      reminder: 'text-yellow-600 bg-yellow-50',
      announcement: 'text-indigo-600 bg-indigo-50'
    };
    return colors[type as keyof typeof colors] || 'text-gray-600 bg-gray-50';
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-red-100 text-red-800'
    };
    return (
      <Badge className={variants[priority as keyof typeof variants]}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const handleSettingChange = (key: string, value: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 48) return 'Yesterday';
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notifications & Alerts</h1>
          <p className="text-muted-foreground">
            Stay updated with booking reminders, payment alerts, and announcements
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge className="bg-blue-100 text-blue-800">
            {unreadCount} unread
          </Badge>
          <Button variant="outline" onClick={markAllAsRead}>
            <MarkAsRead className="mr-2 h-4 w-4" />
            Mark All Read
          </Button>
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Notification Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold">{notifications.length}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <div className="text-2xl font-bold">{unreadCount}</div>
                <div className="text-sm text-muted-foreground">Unread</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold">
                  {notifications.filter(n => n.type === 'payment').length}
                </div>
                <div className="text-sm text-muted-foreground">Payment</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-purple-600" />
              <div>
                <div className="text-2xl font-bold">
                  {notifications.filter(n => n.type === 'booking').length}
                </div>
                <div className="text-sm text-muted-foreground">Booking</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Notifications List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Notifications</CardTitle>
                <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="unread">Unread</SelectItem>
                    <SelectItem value="booking">Booking</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="promotion">Promotions</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredNotifications.map((notification) => {
                  const Icon = getNotificationIcon(notification.type);
                  const colorClass = getNotificationColor(notification.type, notification.priority);
                  
                  return (
                    <div
                      key={notification.id}
                      className={`p-4 border rounded-lg transition-colors ${
                        !notification.isRead ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${colorClass}`}>
                          <Icon className="h-5 w-5" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <div className="font-semibold text-sm">
                              {notification.title}
                            </div>
                            <div className="flex items-center space-x-2">
                              {getPriorityBadge(notification.priority)}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  {!notification.isRead && (
                                    <DropdownMenuItem onClick={() => markAsRead(notification.id)}>
                                      <Eye className="mr-2 h-4 w-4" />
                                      Mark as Read
                                    </DropdownMenuItem>
                                  )}
                                  <DropdownMenuItem>
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archive
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    className="text-red-600"
                                    onClick={() => deleteNotification(notification.id)}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {notification.message}
                          </p>
                          <div className="flex items-center justify-between">
                            <div className="text-xs text-muted-foreground">
                              {formatTimestamp(notification.timestamp)}
                            </div>
                            {notification.metadata?.hostelName && (
                              <div className="text-xs text-muted-foreground">
                                {notification.metadata.hostelName}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {filteredNotifications.length === 0 && (
                  <div className="text-center py-8">
                    <Bell className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No notifications</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {filter === 'unread' 
                        ? "You're all caught up! No unread notifications."
                        : "No notifications match your current filter."
                      }
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Notification Settings */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Customize how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-medium mb-4">Delivery Methods</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications via email
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.emailNotifications}
                      onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Browser push notifications
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.pushNotifications}
                      onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>SMS Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Text message alerts
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.smsNotifications}
                      onCheckedChange={(checked) => handleSettingChange('smsNotifications', checked)}
                    />
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-4">Notification Types</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Booking Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Check-in and check-out reminders
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.bookingReminders}
                      onCheckedChange={(checked) => handleSettingChange('bookingReminders', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Payment Alerts</Label>
                      <p className="text-sm text-muted-foreground">
                        Payment due dates and confirmations
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.paymentAlerts}
                      onCheckedChange={(checked) => handleSettingChange('paymentAlerts', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Promotional Offers</Label>
                      <p className="text-sm text-muted-foreground">
                        Special deals and discounts
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.promotionalOffers}
                      onCheckedChange={(checked) => handleSettingChange('promotionalOffers', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>System Updates</Label>
                      <p className="text-sm text-muted-foreground">
                        Platform updates and maintenance
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.systemUpdates}
                      onCheckedChange={(checked) => handleSettingChange('systemUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Review Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Reminders to review your stays
                      </p>
                    </div>
                    <Switch
                      checked={notificationSettings.reviewReminders}
                      onCheckedChange={(checked) => handleSettingChange('reviewReminders', checked)}
                    />
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button className="w-full">
                  <Settings className="mr-2 h-4 w-4" />
                  Save Settings
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <BellOff className="mr-2 h-4 w-4" />
                Snooze All for 1 Hour
              </Button>

              <Button variant="outline" className="w-full justify-start">
                <Archive className="mr-2 h-4 w-4" />
                Archive All Read
              </Button>

              <Button variant="outline" className="w-full justify-start">
                <Trash2 className="mr-2 h-4 w-4" />
                Clear All Notifications
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
