import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Calendar, 
  Search, 
  Eye,
  Edit,
  XCircle,
  CheckCircle,
  Clock,
  MapPin,
  Bed,
  Star,
  Download,
  MessageSquare,
  CreditCard
} from 'lucide-react';
import { mockBookings, mockHostels, getHostelById } from '@/data/mockData';

export const MyBookings: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'confirmed' | 'pending' | 'cancelled' | 'completed'>('all');
  
  // In a real app, this would filter by the current user's ID
  // For demo purposes, we'll show all bookings
  const userBookings = mockBookings;

  const filteredBookings = userBookings.filter(booking => {
    const hostel = getHostelById(booking.hostelId);
    const matchesSearch = hostel?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.bedNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      confirmed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle },
      completed: { color: 'bg-blue-100 text-blue-800', icon: CheckCircle },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={config.color}>
        <config.icon className="mr-1 h-3 w-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getPaymentBadge = (status: string) => {
    const statusConfig = {
      paid: { color: 'bg-green-100 text-green-800' },
      pending: { color: 'bg-yellow-100 text-yellow-800' },
      failed: { color: 'bg-red-100 text-red-800' },
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getBookingStats = () => {
    return {
      total: userBookings.length,
      confirmed: userBookings.filter(b => b.status === 'confirmed').length,
      pending: userBookings.filter(b => b.status === 'pending').length,
      cancelled: userBookings.filter(b => b.status === 'cancelled').length,
      totalSpent: userBookings.reduce((sum, b) => sum + b.amount, 0),
    };
  };

  const stats = getBookingStats();

  const isUpcoming = (booking: typeof mockBookings[0]) => {
    return new Date(booking.checkIn) > new Date();
  };

  const isCurrent = (booking: typeof mockBookings[0]) => {
    const now = new Date();
    return new Date(booking.checkIn) <= now && new Date(booking.checkOut) >= now;
  };

  const isPast = (booking: typeof mockBookings[0]) => {
    return new Date(booking.checkOut) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Bookings</h1>
          <p className="text-muted-foreground">
            View and manage all your hostel bookings
          </p>
        </div>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Download History
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              All time bookings
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Confirmed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.confirmed}</div>
            <p className="text-xs text-muted-foreground">
              Active bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting confirmation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.cancelled}</div>
            <p className="text-xs text-muted-foreground">
              Cancelled bookings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{(stats.totalSpent / 1000).toFixed(0)}K</div>
            <p className="text-xs text-muted-foreground">
              All time spending
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Bookings List */}
      <Card>
        <CardHeader>
          <CardTitle>Booking History</CardTitle>
          <CardDescription>
            All your hostel bookings and their current status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search bookings by hostel name or bed number..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
            >
              <option value="all">All Bookings</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div className="space-y-4">
            {filteredBookings.map((booking) => {
              const hostel = getHostelById(booking.hostelId);
              const stayDuration = Math.ceil((new Date(booking.checkOut).getTime() - new Date(booking.checkIn).getTime()) / (1000 * 60 * 60 * 24));
              
              return (
                <Card key={booking.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        {/* Hostel Info */}
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-lg">{hostel?.name}</h3>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <MapPin className="mr-1 h-3 w-3" />
                              {hostel?.city}, {hostel?.state}
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            {getStatusBadge(booking.status)}
                            {getPaymentBadge(booking.paymentStatus)}
                          </div>
                        </div>

                        {/* Booking Details */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-muted-foreground">Bed Number</div>
                            <div className="flex items-center font-medium">
                              <Bed className="mr-1 h-3 w-3" />
                              {booking.bedNumber}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Check-in</div>
                            <div className="font-medium">
                              {new Date(booking.checkIn).toLocaleDateString()}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Check-out</div>
                            <div className="font-medium">
                              {new Date(booking.checkOut).toLocaleDateString()}
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Duration</div>
                            <div className="font-medium">{stayDuration} nights</div>
                          </div>
                        </div>

                        {/* Amount and Rating */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div>
                              <div className="text-muted-foreground text-sm">Total Amount</div>
                              <div className="text-lg font-bold">₹{booking.amount.toLocaleString()}</div>
                            </div>
                            {hostel && (
                              <div className="flex items-center space-x-1">
                                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                                <span className="font-medium">{hostel.rating}</span>
                              </div>
                            )}
                          </div>
                          
                          {/* Status Indicators */}
                          <div className="flex items-center space-x-2 text-sm">
                            {isUpcoming(booking) && (
                              <Badge variant="outline" className="text-blue-600">
                                Upcoming
                              </Badge>
                            )}
                            {isCurrent(booking) && (
                              <Badge variant="outline" className="text-green-600">
                                Current Stay
                              </Badge>
                            )}
                            {isPast(booking) && (
                              <Badge variant="outline" className="text-gray-600">
                                Past Stay
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex space-x-2 pt-2">
                          <Button variant="outline" size="sm">
                            <Eye className="mr-1 h-3 w-3" />
                            View Details
                          </Button>
                          
                          {booking.status === 'pending' && (
                            <Button variant="outline" size="sm">
                              <Edit className="mr-1 h-3 w-3" />
                              Modify Booking
                            </Button>
                          )}
                          
                          {booking.status === 'confirmed' && isUpcoming(booking) && (
                            <Button variant="outline" size="sm" className="text-red-600">
                              <XCircle className="mr-1 h-3 w-3" />
                              Cancel Booking
                            </Button>
                          )}
                          
                          {isPast(booking) && booking.status === 'confirmed' && (
                            <Button variant="outline" size="sm">
                              <Star className="mr-1 h-3 w-3" />
                              Rate & Review
                            </Button>
                          )}
                          
                          <Button variant="outline" size="sm">
                            <MessageSquare className="mr-1 h-3 w-3" />
                            Contact Hostel
                          </Button>
                          
                          <Button variant="outline" size="sm">
                            <Download className="mr-1 h-3 w-3" />
                            Receipt
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {filteredBookings.length === 0 && (
            <div className="text-center py-12">
              <Calendar className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">No bookings found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'You haven\'t made any bookings yet.'}
              </p>
              {!searchTerm && (
                <Button className="mt-4">
                  <Search className="mr-2 h-4 w-4" />
                  Find Hostels
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
