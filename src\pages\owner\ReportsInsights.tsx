import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart3, 
  Download,
  TrendingUp,
  TrendingDown,
  FileText,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Activity,
  Star,
  MapPin,
  Clock,
  DollarSign,
  Eye,
  Users,
  Building2,
  Zap,
  Award,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { mockHostels, mockBookings, mockPayments, mockUsers } from '@/data/mockData';

export const ReportsInsights: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [reportType, setReportType] = useState<'overview' | 'financial' | 'operational' | 'competitive'>('overview');

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  
  // Filter bookings and payments for owner's hostels
  const ownerBookings = mockBookings.filter(b => ownerHostels.some(h => h.id === b.hostelId));
  const ownerPayments = mockPayments.filter(p => ownerBookings.some(b => b.id === p.bookingId));

  // Calculate comprehensive metrics
  const totalRevenue = ownerPayments.reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);
  const totalBookings = ownerBookings.length;
  const totalBeds = ownerHostels.reduce((sum, h) => sum + h.totalBeds, 0);
  const occupiedBeds = ownerHostels.reduce((sum, h) => sum + (h.totalBeds - h.availableBeds), 0);
  const occupancyRate = ((occupiedBeds / totalBeds) * 100).toFixed(1);
  const averageRating = (ownerHostels.reduce((sum, h) => sum + h.rating, 0) / ownerHostels.length).toFixed(1);
  const averageBookingValue = totalRevenue / totalBookings || 0;

  // Market insights (mock data for demonstration)
  const marketInsights = {
    marketGrowth: '+15.2%',
    competitorCount: 12,
    marketShare: '8.5%',
    pricePosition: 'Competitive',
    demandTrend: 'Increasing',
    seasonalPeak: 'March-May'
  };

  // Performance benchmarks
  const benchmarks = [
    {
      metric: 'Occupancy Rate',
      yourValue: occupancyRate + '%',
      marketAvg: '75%',
      status: parseFloat(occupancyRate) > 75 ? 'above' : 'below',
      trend: 'up'
    },
    {
      metric: 'Average Rating',
      yourValue: averageRating + '/5.0',
      marketAvg: '4.2/5.0',
      status: parseFloat(averageRating) > 4.2 ? 'above' : 'below',
      trend: 'up'
    },
    {
      metric: 'Revenue per Bed',
      yourValue: `₹${(totalRevenue / totalBeds / 1000).toFixed(0)}K`,
      marketAvg: '₹45K',
      status: (totalRevenue / totalBeds) > 45000 ? 'above' : 'below',
      trend: 'up'
    },
    {
      metric: 'Booking Conversion',
      yourValue: '12.5%',
      marketAvg: '10.8%',
      status: 'above',
      trend: 'up'
    }
  ];

  // Monthly performance data (mock)
  const monthlyData = [
    { month: 'Jan', revenue: 85000, bookings: 28, occupancy: 72 },
    { month: 'Feb', revenue: 92000, bookings: 31, occupancy: 78 },
    { month: 'Mar', revenue: 105000, bookings: 35, occupancy: 85 },
    { month: 'Apr', revenue: 118000, bookings: 42, occupancy: 88 },
    { month: 'May', revenue: 125000, bookings: 45, occupancy: 92 },
    { month: 'Jun', revenue: 132000, bookings: 48, occupancy: 95 }
  ];

  // Competitive analysis (mock data)
  const competitors = [
    {
      name: 'City Center Hostel',
      rating: 4.3,
      avgPrice: 1200,
      occupancy: 82,
      distance: '0.5 km',
      strengths: ['Location', 'Amenities'],
      weaknesses: ['Price', 'Service']
    },
    {
      name: 'Budget Stay Inn',
      rating: 3.9,
      avgPrice: 800,
      occupancy: 75,
      distance: '1.2 km',
      strengths: ['Price', 'Cleanliness'],
      weaknesses: ['Location', 'Facilities']
    },
    {
      name: 'Premium Hostel',
      rating: 4.6,
      avgPrice: 1800,
      occupancy: 88,
      distance: '0.8 km',
      strengths: ['Service', 'Facilities'],
      weaknesses: ['Price']
    }
  ];

  const reportStats = [
    {
      title: 'Total Revenue',
      value: `₹${(totalRevenue / 1000).toFixed(0)}K`,
      change: '+18.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'This period'
    },
    {
      title: 'Market Position',
      value: '#3',
      change: '+1 rank',
      trend: 'up',
      icon: Award,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'In local area'
    },
    {
      title: 'Growth Rate',
      value: '+24.2%',
      change: '****%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'YoY growth'
    },
    {
      title: 'Efficiency Score',
      value: '87/100',
      change: '+12 pts',
      trend: 'up',
      icon: Target,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Operational efficiency'
    },
  ];

  const getTimeRangeLabel = (range: string) => {
    const labels = {
      '7d': 'Last 7 days',
      '30d': 'Last 30 days',
      '90d': 'Last 90 days',
      '1y': 'Last year'
    };
    return labels[range as keyof typeof labels];
  };

  const getBenchmarkStatus = (status: string) => {
    return status === 'above' ? (
      <Badge className="bg-green-100 text-green-800">
        <ArrowUpRight className="mr-1 h-3 w-3" />
        Above Market
      </Badge>
    ) : (
      <Badge className="bg-red-100 text-red-800">
        <ArrowDownRight className="mr-1 h-3 w-3" />
        Below Market
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Insights</h1>
          <p className="text-muted-foreground">
            Comprehensive business reports and market analysis
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={reportType} onValueChange={(value: any) => setReportType(value)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="overview">Overview Report</SelectItem>
              <SelectItem value="financial">Financial Report</SelectItem>
              <SelectItem value="operational">Operational Report</SelectItem>
              <SelectItem value="competitive">Competitive Analysis</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Generate PDF
          </Button>
        </div>
      </div>

      {/* Report Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {reportStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last period</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Performance Benchmarks */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Performance Benchmarks
          </CardTitle>
          <CardDescription>
            Compare your performance against market averages
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {benchmarks.map((benchmark, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div>
                    <div className="font-medium">{benchmark.metric}</div>
                    <div className="text-sm text-muted-foreground">
                      Market Average: {benchmark.marketAvg}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-2xl font-bold">{benchmark.yourValue}</div>
                    <div className="text-xs text-muted-foreground">Your Performance</div>
                  </div>
                  {getBenchmarkStatus(benchmark.status)}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Monthly Performance Trends
          </CardTitle>
          <CardDescription>
            Track your business performance over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {monthlyData.map((data, index) => (
              <div key={data.month} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="font-medium w-12">{data.month}</div>
                  <div className="grid grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-600">₹{(data.revenue / 1000).toFixed(0)}K</div>
                      <div className="text-xs text-muted-foreground">Revenue</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-600">{data.bookings}</div>
                      <div className="text-xs text-muted-foreground">Bookings</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-600">{data.occupancy}%</div>
                      <div className="text-xs text-muted-foreground">Occupancy</div>
                    </div>
                  </div>
                </div>
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${data.occupancy}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Competitive Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Competitive Analysis
          </CardTitle>
          <CardDescription>
            Compare your hostel with nearby competitors
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {competitors.map((competitor, index) => (
              <div key={competitor.name} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                      <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                    </div>
                    <div>
                      <div className="font-semibold">{competitor.name}</div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="mr-1 h-3 w-3" />
                        {competitor.distance} away
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">{competitor.rating}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4 mb-3">
                  <div className="text-center">
                    <div className="text-lg font-bold">₹{competitor.avgPrice}</div>
                    <div className="text-xs text-muted-foreground">Avg Price</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold">{competitor.occupancy}%</div>
                    <div className="text-xs text-muted-foreground">Occupancy</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold">{competitor.rating}</div>
                    <div className="text-xs text-muted-foreground">Rating</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-green-600 mb-1">Strengths</div>
                    <div className="flex flex-wrap gap-1">
                      {competitor.strengths.map(strength => (
                        <Badge key={strength} className="bg-green-100 text-green-800 text-xs">
                          {strength}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-red-600 mb-1">Weaknesses</div>
                    <div className="flex flex-wrap gap-1">
                      {competitor.weaknesses.map(weakness => (
                        <Badge key={weakness} className="bg-red-100 text-red-800 text-xs">
                          {weakness}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
