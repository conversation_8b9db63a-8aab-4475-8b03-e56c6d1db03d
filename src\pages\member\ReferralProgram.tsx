import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Users, 
  Share2,
  Copy,
  Mail,
  MessageSquare,
  Gift,
  Star,
  Trophy,
  DollarSign,
  Calendar,
  CheckCircle,
  Clock,
  Send,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  ArrowUpRight,
  TrendingUp,
  Award,
  Target
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

// Mock referral data
interface Referral {
  id: string;
  friendName: string;
  friendEmail: string;
  status: 'pending' | 'signed_up' | 'completed_booking' | 'rewarded';
  inviteDate: string;
  signupDate?: string;
  bookingDate?: string;
  rewardAmount: number;
  hostelName?: string;
}

const mockReferrals: Referral[] = [
  {
    id: '1',
    friendName: 'John Smith',
    friendEmail: '<EMAIL>',
    status: 'rewarded',
    inviteDate: '2024-01-15',
    signupDate: '2024-01-16',
    bookingDate: '2024-01-18',
    rewardAmount: 500,
    hostelName: 'Urban Stay Hostel'
  },
  {
    id: '2',
    friendName: 'Sarah Wilson',
    friendEmail: '<EMAIL>',
    status: 'completed_booking',
    inviteDate: '2024-01-20',
    signupDate: '2024-01-21',
    bookingDate: '2024-01-22',
    rewardAmount: 500,
    hostelName: 'Elite Residency'
  },
  {
    id: '3',
    friendName: 'Mike Johnson',
    friendEmail: '<EMAIL>',
    status: 'signed_up',
    inviteDate: '2024-01-22',
    signupDate: '2024-01-23',
    rewardAmount: 0
  },
  {
    id: '4',
    friendName: 'Emma Davis',
    friendEmail: '<EMAIL>',
    status: 'pending',
    inviteDate: '2024-01-24',
    rewardAmount: 0
  }
];

// Mock rewards data
interface Reward {
  id: string;
  type: 'referral_bonus' | 'milestone_bonus' | 'special_reward';
  title: string;
  amount: number;
  date: string;
  description: string;
  status: 'pending' | 'credited';
}

const mockRewards: Reward[] = [
  {
    id: '1',
    type: 'referral_bonus',
    title: 'Referral Bonus - John Smith',
    amount: 500,
    date: '2024-01-18',
    description: 'Friend completed first booking',
    status: 'credited'
  },
  {
    id: '2',
    type: 'milestone_bonus',
    title: '5 Referrals Milestone',
    amount: 1000,
    date: '2024-01-20',
    description: 'Bonus for reaching 5 successful referrals',
    status: 'credited'
  },
  {
    id: '3',
    type: 'referral_bonus',
    title: 'Referral Bonus - Sarah Wilson',
    amount: 500,
    date: '2024-01-22',
    description: 'Friend completed first booking',
    status: 'pending'
  }
];

export const ReferralProgram: React.FC = () => {
  const { user } = useAuth();
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteEmails, setInviteEmails] = useState('');
  const [personalMessage, setPersonalMessage] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'referrals' | 'rewards'>('overview');

  // Mock referral code and stats
  const referralCode = 'JOHN2024';
  const referralLink = `https://hostelhub.com/signup?ref=${referralCode}`;
  
  const totalReferrals = mockReferrals.length;
  const successfulReferrals = mockReferrals.filter(r => r.status === 'rewarded' || r.status === 'completed_booking').length;
  const totalEarnings = mockRewards.filter(r => r.status === 'credited').reduce((sum, r) => sum + r.amount, 0);
  const pendingEarnings = mockRewards.filter(r => r.status === 'pending').reduce((sum, r) => sum + r.amount, 0);

  const referralStats = [
    {
      title: 'Total Referrals',
      value: totalReferrals.toString(),
      change: '+2',
      trend: 'up',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Successful Referrals',
      value: successfulReferrals.toString(),
      change: '+1',
      trend: 'up',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Total Earnings',
      value: `₹${totalEarnings.toLocaleString()}`,
      change: '+₹500',
      trend: 'up',
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Pending Rewards',
      value: `₹${pendingEarnings.toLocaleString()}`,
      change: '+₹500',
      trend: 'up',
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      signed_up: 'bg-blue-100 text-blue-800',
      completed_booking: 'bg-green-100 text-green-800',
      rewarded: 'bg-purple-100 text-purple-800'
    };
    const labels = {
      pending: 'Pending',
      signed_up: 'Signed Up',
      completed_booking: 'Booked',
      rewarded: 'Rewarded'
    };
    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {labels[status as keyof typeof labels] || status}
      </Badge>
    );
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // In a real app, you'd show a toast notification
    console.log('Copied to clipboard:', text);
  };

  const handleSendInvites = () => {
    // In a real app, this would make an API call
    console.log('Sending invites to:', inviteEmails);
    console.log('Personal message:', personalMessage);
    setIsInviteDialogOpen(false);
    setInviteEmails('');
    setPersonalMessage('');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Referral Program</h1>
          <p className="text-muted-foreground">
            Invite friends and earn rewards for every successful referral
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Share2 className="mr-2 h-4 w-4" />
            Share Link
          </Button>
          <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Send className="mr-2 h-4 w-4" />
                Invite Friends
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Invite Friends</DialogTitle>
                <DialogDescription>
                  Send personalized invitations to your friends
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="emails">Email Addresses</Label>
                  <Input
                    id="emails"
                    value={inviteEmails}
                    onChange={(e) => setInviteEmails(e.target.value)}
                    placeholder="Enter email addresses separated by commas"
                  />
                  <p className="text-sm text-muted-foreground">
                    Separate multiple email addresses with commas
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Personal Message (Optional)</Label>
                  <textarea
                    id="message"
                    value={personalMessage}
                    onChange={(e) => setPersonalMessage(e.target.value)}
                    className="w-full p-3 border rounded-md resize-none"
                    rows={4}
                    placeholder="Add a personal message to your invitation..."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSendInvites}>
                  <Send className="mr-2 h-4 w-4" />
                  Send Invitations
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Referral Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {referralStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                <span className="text-green-600">{stat.change}</span>
                <span className="ml-1">this month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Target className="inline-block mr-2 h-4 w-4" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('referrals')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'referrals'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Users className="inline-block mr-2 h-4 w-4" />
            My Referrals
          </button>
          <button
            onClick={() => setActiveTab('rewards')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'rewards'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Gift className="inline-block mr-2 h-4 w-4" />
            Rewards
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Referral Link Card */}
          <Card>
            <CardHeader>
              <CardTitle>Your Referral Link</CardTitle>
              <CardDescription>
                Share this link with friends to earn rewards
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Referral Code</Label>
                <div className="flex items-center space-x-2">
                  <Input value={referralCode} readOnly />
                  <Button variant="outline" onClick={() => copyToClipboard(referralCode)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Referral Link</Label>
                <div className="flex items-center space-x-2">
                  <Input value={referralLink} readOnly className="text-sm" />
                  <Button variant="outline" onClick={() => copyToClipboard(referralLink)}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="pt-4">
                <h4 className="font-medium mb-3">Share on Social Media</h4>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Facebook className="mr-2 h-4 w-4" />
                    Facebook
                  </Button>
                  <Button variant="outline" size="sm">
                    <Twitter className="mr-2 h-4 w-4" />
                    Twitter
                  </Button>
                  <Button variant="outline" size="sm">
                    <Instagram className="mr-2 h-4 w-4" />
                    Instagram
                  </Button>
                  <Button variant="outline" size="sm">
                    <Linkedin className="mr-2 h-4 w-4" />
                    LinkedIn
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* How It Works */}
          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
              <CardDescription>
                Earn rewards in 3 simple steps
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
                    1
                  </div>
                  <div>
                    <div className="font-medium">Share Your Link</div>
                    <div className="text-sm text-muted-foreground">
                      Send your referral link to friends via email, social media, or messaging
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full text-sm font-semibold">
                    2
                  </div>
                  <div>
                    <div className="font-medium">Friend Signs Up</div>
                    <div className="text-sm text-muted-foreground">
                      Your friend creates an account using your referral link
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-semibold">
                    3
                  </div>
                  <div>
                    <div className="font-medium">Earn Rewards</div>
                    <div className="text-sm text-muted-foreground">
                      Get ₹500 when your friend completes their first booking
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <Trophy className="h-5 w-5 text-purple-600" />
                  <div className="font-semibold text-purple-900">Bonus Rewards</div>
                </div>
                <div className="text-sm text-purple-800">
                  Earn milestone bonuses: ₹1,000 for every 5 successful referrals!
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'referrals' && (
        <Card>
          <CardHeader>
            <CardTitle>My Referrals</CardTitle>
            <CardDescription>
              Track the status of your friend referrals
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Friend</TableHead>
                    <TableHead>Invite Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Hostel</TableHead>
                    <TableHead>Reward</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockReferrals.map((referral) => (
                    <TableRow key={referral.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{referral.friendName}</div>
                          <div className="text-sm text-muted-foreground">
                            {referral.friendEmail}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(referral.inviteDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(referral.status)}
                      </TableCell>
                      <TableCell>
                        {referral.hostelName || '-'}
                      </TableCell>
                      <TableCell>
                        {referral.rewardAmount > 0 ? (
                          <div className="font-semibold text-green-600">
                            ₹{referral.rewardAmount}
                          </div>
                        ) : (
                          <div className="text-muted-foreground">-</div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {activeTab === 'rewards' && (
        <Card>
          <CardHeader>
            <CardTitle>Reward History</CardTitle>
            <CardDescription>
              View your earned rewards and pending payouts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRewards.map((reward) => (
                <div key={reward.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className={`p-2 rounded-lg ${
                      reward.type === 'referral_bonus' ? 'bg-blue-50' :
                      reward.type === 'milestone_bonus' ? 'bg-purple-50' : 'bg-green-50'
                    }`}>
                      {reward.type === 'referral_bonus' ? (
                        <Users className={`h-5 w-5 ${
                          reward.type === 'referral_bonus' ? 'text-blue-600' :
                          reward.type === 'milestone_bonus' ? 'text-purple-600' : 'text-green-600'
                        }`} />
                      ) : reward.type === 'milestone_bonus' ? (
                        <Trophy className="h-5 w-5 text-purple-600" />
                      ) : (
                        <Award className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium">{reward.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {reward.description}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(reward.date).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-lg text-green-600">
                      +₹{reward.amount}
                    </div>
                    <Badge className={
                      reward.status === 'credited'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }>
                      {reward.status === 'credited' ? 'Credited' : 'Pending'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
