import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  BarChart3, 
  Download,
  TrendingUp,
  TrendingDown,
  Building2,
  Users,
  Bed,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Target,
  Activity,
  Star,
  MapPin,
  Clock,
  DollarSign,
  Eye,
  Zap
} from 'lucide-react';
import { mockHostels, mockBookings, mockPayments, mockUsers, getUserById } from '@/data/mockData';

export const PropertyAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedHostel, setSelectedHostel] = useState<'all' | string>('all');

  // Get current owner's hostels (assuming owner ID '2' for demo)
  const currentOwnerId = '2';
  const ownerHostels = mockHostels.filter(h => h.ownerId === currentOwnerId);
  
  // Filter bookings for owner's hostels
  const ownerBookings = mockBookings.filter(b => ownerHostels.some(h => h.id === b.hostelId));

  // Calculate analytics metrics
  const totalBeds = ownerHostels.reduce((sum, h) => sum + h.totalBeds, 0);
  const occupiedBeds = ownerHostels.reduce((sum, h) => sum + (h.totalBeds - h.availableBeds), 0);
  const overallOccupancyRate = ((occupiedBeds / totalBeds) * 100).toFixed(1);
  const averageRating = (ownerHostels.reduce((sum, h) => sum + h.rating, 0) / ownerHostels.length).toFixed(1);
  const totalBookings = ownerBookings.length;
  const averageStayDuration = ownerBookings.reduce((sum, booking) => {
    const checkIn = new Date(booking.checkIn);
    const checkOut = new Date(booking.checkOut);
    const duration = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
    return sum + duration;
  }, 0) / ownerBookings.length || 0;

  // Property performance analysis
  const propertyPerformance = ownerHostels.map(hostel => {
    const hostelBookings = ownerBookings.filter(b => b.hostelId === hostel.id);
    const hostelPayments = mockPayments.filter(p => hostelBookings.some(b => b.id === p.bookingId));
    const revenue = hostelPayments.reduce((sum, p) => sum + p.splitDetails.hostelOwner, 0);
    const occupancyRate = ((hostel.totalBeds - hostel.availableBeds) / hostel.totalBeds) * 100;
    const avgStayDuration = hostelBookings.reduce((sum, booking) => {
      const checkIn = new Date(booking.checkIn);
      const checkOut = new Date(booking.checkOut);
      const duration = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
      return sum + duration;
    }, 0) / hostelBookings.length || 0;

    return {
      ...hostel,
      bookings: hostelBookings.length,
      revenue,
      occupancyRate: occupancyRate.toFixed(1),
      avgStayDuration: avgStayDuration.toFixed(0),
      revenuePerBed: (revenue / hostel.totalBeds).toFixed(0)
    };
  }).sort((a, b) => b.revenue - a.revenue);

  // Booking trends (mock data for demonstration)
  const bookingTrends = [
    { month: 'Jan', bookings: 45, occupancy: 78 },
    { month: 'Feb', bookings: 52, occupancy: 82 },
    { month: 'Mar', bookings: 48, occupancy: 75 },
    { month: 'Apr', bookings: 61, occupancy: 88 },
    { month: 'May', bookings: 58, occupancy: 85 },
    { month: 'Jun', bookings: 65, occupancy: 92 }
  ];

  const analyticsStats = [
    {
      title: 'Overall Occupancy',
      value: `${overallOccupancyRate}%`,
      change: '****%',
      trend: 'up',
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      description: 'Across all properties'
    },
    {
      title: 'Total Bookings',
      value: totalBookings.toString(),
      change: '+12.3%',
      trend: 'up',
      icon: Calendar,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      description: 'This period'
    },
    {
      title: 'Avg Stay Duration',
      value: `${averageStayDuration.toFixed(0)} days`,
      change: '+2.1%',
      trend: 'up',
      icon: Clock,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      description: 'Per booking'
    },
    {
      title: 'Average Rating',
      value: `${averageRating}/5.0`,
      change: '+0.3',
      trend: 'up',
      icon: Star,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      description: 'Guest satisfaction'
    },
  ];

  const getTimeRangeLabel = (range: string) => {
    const labels = {
      '7d': 'Last 7 days',
      '30d': 'Last 30 days',
      '90d': 'Last 90 days',
      '1y': 'Last year'
    };
    return labels[range as keyof typeof labels];
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Property Analytics</h1>
          <p className="text-muted-foreground">
            Analyze occupancy rates, booking trends, and property performance
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedHostel} onValueChange={setSelectedHostel}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              {ownerHostels.map(hostel => (
                <SelectItem key={hostel.id} value={hostel.id}>{hostel.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Analytics Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {analyticsStats.map((stat, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.trend === 'up' ? (
                  <ArrowUpRight className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownRight className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                  {stat.change}
                </span>
                <span className="ml-1">from last period</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Booking Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Booking Trends
          </CardTitle>
          <CardDescription>
            Monthly booking volume and occupancy trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {bookingTrends.map((trend, index) => (
              <div key={trend.month} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="font-medium w-12">{trend.month}</div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{trend.bookings} bookings</span>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="font-semibold">{trend.occupancy}%</div>
                    <div className="text-xs text-muted-foreground">Occupancy</div>
                  </div>
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${trend.occupancy}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Property Performance Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Property Performance Comparison
          </CardTitle>
          <CardDescription>
            Detailed performance metrics for each property
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {propertyPerformance.map((property, index) => (
              <div key={property.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                      <span className="text-sm font-semibold text-primary">#{index + 1}</span>
                    </div>
                    <div>
                      <div className="font-semibold">{property.name}</div>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <MapPin className="mr-1 h-3 w-3" />
                        {property.city}, {property.state}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium">{property.rating}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{property.occupancyRate}%</div>
                    <div className="text-xs text-muted-foreground">Occupancy</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{property.bookings}</div>
                    <div className="text-xs text-muted-foreground">Bookings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">₹{(property.revenue / 1000).toFixed(0)}K</div>
                    <div className="text-xs text-muted-foreground">Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{property.avgStayDuration}</div>
                    <div className="text-xs text-muted-foreground">Avg Stay (days)</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">₹{property.revenuePerBed}</div>
                    <div className="text-xs text-muted-foreground">Revenue/Bed</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
