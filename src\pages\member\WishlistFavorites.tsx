import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Heart, 
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Share2,
  Trash2,
  MapPin,
  Star,
  DollarSign,
  Wifi,
  Car,
  Coffee,
  Dumbbell,
  Users,
  Building2,
  Calendar,
  Bookmark,
  Grid3X3,
  List,
  SortAsc,
  SortDesc,
  Plus
} from 'lucide-react';
import { mockHostels } from '@/data/mockData';

// Mock wishlist data
interface WishlistItem {
  id: string;
  hostelId: string;
  dateAdded: string;
  notes?: string;
  priceAlert?: {
    enabled: boolean;
    targetPrice: number;
  };
}

const mockWishlist: WishlistItem[] = [
  {
    id: '1',
    hostelId: '1',
    dateAdded: '2024-01-20',
    notes: 'Great location near college',
    priceAlert: {
      enabled: true,
      targetPrice: 7000
    }
  },
  {
    id: '2',
    hostelId: '3',
    dateAdded: '2024-01-18',
    notes: 'Luxury amenities',
    priceAlert: {
      enabled: false,
      targetPrice: 10000
    }
  }
];

// Mock saved searches
interface SavedSearch {
  id: string;
  name: string;
  location: string;
  priceRange: string;
  amenities: string[];
  dateCreated: string;
  alertsEnabled: boolean;
}

const mockSavedSearches: SavedSearch[] = [
  {
    id: '1',
    name: 'Budget Hostels in Mumbai',
    location: 'Mumbai',
    priceRange: '5000-8000',
    amenities: ['wifi', 'ac'],
    dateCreated: '2024-01-15',
    alertsEnabled: true
  },
  {
    id: '2',
    name: 'Premium Hostels in Bangalore',
    location: 'Bangalore',
    priceRange: '10000-15000',
    amenities: ['gym', 'swimming_pool', 'wifi'],
    dateCreated: '2024-01-12',
    alertsEnabled: false
  }
];

export const WishlistFavorites: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'wishlist' | 'searches' | 'alerts'>('wishlist');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date_added' | 'price' | 'rating'>('date_added');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [locationFilter, setLocationFilter] = useState<'all' | 'mumbai' | 'pune' | 'bangalore'>('all');

  // Get wishlist hostels
  const wishlistHostels = mockWishlist.map(item => {
    const hostel = mockHostels.find(h => h.id === item.hostelId);
    return hostel ? { ...hostel, wishlistItem: item } : null;
  }).filter(Boolean);

  // Filter and sort wishlist
  const filteredWishlist = wishlistHostels.filter(hostel => {
    if (!hostel) return false;
    const matchesSearch = hostel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hostel.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLocation = locationFilter === 'all' || 
                           hostel.city.toLowerCase() === locationFilter;
    return matchesSearch && matchesLocation;
  });

  const getAmenityIcon = (amenity: string) => {
    const icons: { [key: string]: any } = {
      wifi: Wifi,
      ac: Coffee,
      gym: Dumbbell,
      parking: Car,
      swimming_pool: Users
    };
    return icons[amenity] || Coffee;
  };

  const formatPrice = (price: number) => {
    return `₹${(price / 1000).toFixed(0)}K`;
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Wishlist & Favorites</h1>
          <p className="text-muted-foreground">
            Save hostels you love and get notified about price drops
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Share2 className="mr-2 h-4 w-4" />
            Share Wishlist
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Save Search
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Heart className="h-5 w-5 text-red-500" />
              <div>
                <div className="text-2xl font-bold">{mockWishlist.length}</div>
                <div className="text-sm text-muted-foreground">Saved Hostels</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Search className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{mockSavedSearches.length}</div>
                <div className="text-sm text-muted-foreground">Saved Searches</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">
                  {mockWishlist.filter(w => w.priceAlert?.enabled).length}
                </div>
                <div className="text-sm text-muted-foreground">Price Alerts</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">3</div>
                <div className="text-sm text-muted-foreground">Cities</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('wishlist')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'wishlist'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Heart className="inline-block mr-2 h-4 w-4" />
            My Wishlist
          </button>
          <button
            onClick={() => setActiveTab('searches')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'searches'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <Bookmark className="inline-block mr-2 h-4 w-4" />
            Saved Searches
          </button>
          <button
            onClick={() => setActiveTab('alerts')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'alerts'
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-gray-300'
            }`}
          >
            <DollarSign className="inline-block mr-2 h-4 w-4" />
            Price Alerts
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'wishlist' && (
        <div className="space-y-6">
          {/* Filters and Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search saved hostels..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-80"
                />
              </div>
              <Select value={locationFilter} onValueChange={(value: any) => setLocationFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Cities</SelectItem>
                  <SelectItem value="mumbai">Mumbai</SelectItem>
                  <SelectItem value="pune">Pune</SelectItem>
                  <SelectItem value="bangalore">Bangalore</SelectItem>
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date_added">Date Added</SelectItem>
                  <SelectItem value="price">Price</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Wishlist Items */}
          {viewMode === 'grid' ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {filteredWishlist.map((hostel) => (
                <Card key={hostel?.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-0">
                    <div className="relative">
                      <img
                        src={hostel?.images[0] || '/placeholder.svg'}
                        alt={hostel?.name}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                      <div className="absolute top-2 right-2">
                        <Button size="sm" variant="secondary" className="bg-white/80 hover:bg-white">
                          <Heart className="h-4 w-4 text-red-500 fill-current" />
                        </Button>
                      </div>
                      {hostel?.wishlistItem?.priceAlert?.enabled && (
                        <Badge className="absolute top-2 left-2 bg-green-100 text-green-800">
                          Price Alert
                        </Badge>
                      )}
                    </div>
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold text-lg">{hostel?.name}</h3>
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span className="text-sm font-medium">{hostel?.rating}</span>
                        </div>
                      </div>
                      <div className="flex items-center text-muted-foreground mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span className="text-sm">{hostel?.city}, {hostel?.state}</span>
                      </div>
                      <div className="flex items-center justify-between mb-3">
                        <div className="text-2xl font-bold text-primary">
                          {formatPrice(hostel?.pricePerBed || 0)}
                        </div>
                        <div className="text-sm text-muted-foreground">per month</div>
                      </div>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {hostel?.amenities.slice(0, 3).map((amenity) => {
                          const Icon = getAmenityIcon(amenity.toLowerCase());
                          return (
                            <Badge key={amenity} variant="secondary" className="text-xs">
                              <Icon className="h-3 w-3 mr-1" />
                              {amenity}
                            </Badge>
                          );
                        })}
                        {hostel && hostel.amenities.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{hostel.amenities.length - 3} more
                          </Badge>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button className="flex-1" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Share2 className="mr-2 h-4 w-4" />
                              Share
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <DollarSign className="mr-2 h-4 w-4" />
                              Set Price Alert
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove from Wishlist
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                      {hostel?.wishlistItem?.notes && (
                        <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                          <strong>Note:</strong> {hostel.wishlistItem.notes}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredWishlist.map((hostel) => (
                <Card key={hostel?.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-4">
                      <img
                        src={hostel?.images[0] || '/placeholder.svg'}
                        alt={hostel?.name}
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-lg">{hostel?.name}</h3>
                          <div className="flex items-center space-x-2">
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                              <span className="text-sm font-medium">{hostel?.rating}</span>
                            </div>
                            <div className="text-2xl font-bold text-primary">
                              {formatPrice(hostel?.pricePerBed || 0)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center text-muted-foreground mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          <span className="text-sm">{hostel?.city}, {hostel?.state}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex flex-wrap gap-1">
                            {hostel?.amenities.slice(0, 4).map((amenity) => {
                              const Icon = getAmenityIcon(amenity.toLowerCase());
                              return (
                                <Badge key={amenity} variant="secondary" className="text-xs">
                                  <Icon className="h-3 w-3 mr-1" />
                                  {amenity}
                                </Badge>
                              );
                            })}
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm">
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              <Heart className="h-4 w-4 text-red-500 fill-current" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {filteredWishlist.length === 0 && (
            <div className="text-center py-12">
              <Heart className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No saved hostels</h3>
              <p className="mt-1 text-sm text-gray-500">
                Start exploring hostels and save your favorites here.
              </p>
              <Button className="mt-4">
                <Search className="mr-2 h-4 w-4" />
                Browse Hostels
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
